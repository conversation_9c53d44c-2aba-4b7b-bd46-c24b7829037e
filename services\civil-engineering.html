<!DOCTYPE html>
<html style="font-size: 16px;" lang="en"><head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="utf-8">
    <meta name="keywords" content="​Services">
    <meta name="description" content="">
    <title>Services</title>
    <link rel="stylesheet" href="../nicepage.css" media="screen">
<link rel="stylesheet" href="../Services.css" media="screen">
    <script class="u-script" type="text/javascript" src="../jquery.js" defer=""></script>
    <script class="u-script" type="text/javascript" src="../nicepage.js" defer=""></script>
    <meta name="generator" content="Nicepage 7.6.5, nicepage.com">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    
    <link id="u-theme-google-font" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:100,100i,300,300i,400,400i,500,500i,700,700i,900,900i|Open+Sans:300,300i,400,400i,500,500i,600,600i,700,700i,800,800i">
    <link id="u-page-google-font" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Montserrat:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i">
    <script type="application/ld+json">{
		"@context": "http://schema.org",
		"@type": "Organization",
		"name": "",
		"logo": "images/logo.png",
		"sameAs": [
				"https://facebook.com/name",
				"https://twitter.com/name",
				"https://www.instagram.com/name"
		]
}</script>

<script src="https://cdn.tailwindcss.com"></script>
    
<!-- Custom Tailwind Config -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    primary: '#1e5b98',
                    secondary: '#f59e0b',
                }
            }
        }
    }
</script>
    <meta name="theme-color" content="#478ac9">
    <meta name="twitter:site" content="@">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Services">
    <meta name="twitter:description" content="">
    <meta property="og:title" content="Services">
    <meta property="og:type" content="website">
  <meta data-intl-tel-input-cdn-path="intlTelInput/"></head>
  <body data-path-to-root="./" data-include-products="false" class="u-body u-xl-mode" data-lang="en">
    
    <header class=" u-clearfix u-header u-section-row-container" id="header" data-animation-name="" data-animation-duration="0" data-animation-delay="0" data-animation-direction=""><div class="u-section-rows">
        <div class="u-hidden-xs u-section-row" id="sec-71fa">
          <div class="u-clearfix u-sheet u-valign-middle-lg u-valign-middle-md u-valign-middle-sm u-sheet-1">
            <a href="#" class="u-image u-logo u-image-1" data-image-width="602" data-image-height="631">
              <img src="../images/logo.png" class="u-logo-image u-logo-image-1" data-image-width="80">
            </a>
            <div class="u-social-icons u-social-icons-1">
              <a class="u-social-url" title="facebook" target="_blank" href="https://www.facebook.com/share/1AN9a562UX/?mibextid=qi2Omg">
                <span class="u-icon u-social-facebook u-social-icon u-icon-1"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 112 112" style=""><use xlink:href="#svg-7e28"></use></svg><svg class="u-svg-content" viewBox="0 0 112 112" x="0" y="0" id="svg-7e28"><circle fill="currentColor" cx="56.1" cy="56.1" r="55"></circle><path fill="#FFFFFF" d="M73.5,31.6h-9.1c-1.4,0-3.6,0.8-3.6,3.9v8.5h12.6L72,58.3H60.8v40.8H43.9V58.3h-8V43.9h8v-9.2
      c0-6.7,3.1-17,17-17h12.5v13.9H73.5z"></path></svg></span>
              </a>
              <a class="u-social-url" title="twitter" target="_blank" href="https://www.tiktok.com/@janeandpeace?_t=ZM-8v9UA47zw7L&_r=1">
                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="35" height="35" viewBox="0 0 50 50">
                    <path d="M41,4H9C6.243,4,4,6.243,4,9v32c0,2.757,2.243,5,5,5h32c2.757,0,5-2.243,5-5V9C46,6.243,43.757,4,41,4z M37.006,22.323 c-0.227,0.021-0.457,0.035-0.69,0.035c-2.623,0-4.928-1.349-6.269-3.388c0,5.349,0,11.435,0,11.537c0,4.709-3.818,8.527-8.527,8.527 s-8.527-3.818-8.527-8.527s3.818-8.527,8.527-8.527c0.178,0,0.352,0.016,0.527,0.027v4.202c-0.175-0.021-0.347-0.053-0.527-0.053 c-2.404,0-4.352,1.948-4.352,4.352s1.948,4.352,4.352,4.352s4.527-1.894,4.527-4.298c0-0.095,0.042-19.594,0.042-19.594h4.016 c0.378,3.591,3.277,6.425,6.901,6.685V22.323z"></path>
                </svg>
              </a>
              <a class="u-social-url" title="instagram" target="_blank" href="https://www.instagram.com/p/DH4H_NWIZzT/?igsh=c21tMjVtMXpmZWM=">
                <span class="u-icon u-social-icon u-social-instagram u-icon-3"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 112 112" style=""><use xlink:href="#svg-d54d"></use></svg><svg class="u-svg-content" viewBox="0 0 112 112" x="0" y="0" id="svg-d54d"><circle fill="currentColor" cx="56.1" cy="56.1" r="55"></circle><path fill="#FFFFFF" d="M55.9,38.2c-9.9,0-17.9,8-17.9,17.9C38,66,46,74,55.9,74c9.9,0,17.9-8,17.9-17.9C73.8,46.2,65.8,38.2,55.9,38.2
      z M55.9,66.4c-5.7,0-10.3-4.6-10.3-10.3c-0.1-5.7,4.6-10.3,10.3-10.3c5.7,0,10.3,4.6,10.3,10.3C66.2,61.8,61.6,66.4,55.9,66.4z"></path><path fill="#FFFFFF" d="M74.3,33.5c-2.3,0-4.2,1.9-4.2,4.2s1.9,4.2,4.2,4.2s4.2-1.9,4.2-4.2S76.6,33.5,74.3,33.5z"></path><path fill="#FFFFFF" d="M73.1,21.3H38.6c-9.7,0-17.5,7.9-17.5,17.5v34.5c0,9.7,7.9,17.6,17.5,17.6h34.5c9.7,0,17.5-7.9,17.5-17.5V38.8
      C90.6,29.1,82.7,21.3,73.1,21.3z M83,73.3c0,5.5-4.5,9.9-9.9,9.9H38.6c-5.5,0-9.9-4.5-9.9-9.9V38.8c0-5.5,4.5-9.9,9.9-9.9h34.5
      c5.5,0,9.9,4.5,9.9,9.9V73.3z"></path></svg></span>
              </a>
            </div>
          </div>
          
          
        </div>
        <div class="u-grey-5 u-section-row u-section-row-2" id="sec-13b1">
          <div class="u-clearfix u-sheet u-valign-middle-lg u-valign-middle-md u-valign-middle-sm u-valign-middle-xl u-sheet-2">
            <a href="#" class="u-hidden-md u-hidden-sm u-hidden-xl u-image u-logo u-image-2" data-image-width="602" data-image-height="631">
              <img src="images/logo.png" class="u-logo-image u-logo-image-2" data-image-width="80">
            </a>
            <nav class="u-menu u-menu-one-level u-offcanvas u-menu-1">
              <div class="menu-collapse" style="font-size: 1rem; letter-spacing: 0px; font-weight: 700; text-transform: uppercase;">
                <a class="u-button-style u-custom-active-border-color u-custom-border u-custom-border-color u-custom-borders u-custom-hover-border-color u-custom-left-right-menu-spacing u-custom-text-active-color u-custom-text-color u-custom-text-hover-color u-custom-top-bottom-menu-spacing u-hamburger-link u-nav-link" href="#">
                  <svg class="u-svg-link" viewBox="0 0 24 24"><use xlink:href="#menu-hamburger"></use></svg>
                  <svg class="u-svg-content" version="1.1" id="menu-hamburger" viewBox="0 0 16 16" x="0px" y="0px" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg"><g><rect y="1" width="16" height="2"></rect><rect y="7" width="16" height="2"></rect><rect y="13" width="16" height="2"></rect>
</g></svg>
                </a>
              </div>
              <div class="u-custom-menu u-nav-container">
                <ul class="u-nav u-unstyled u-nav-1"><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="./" style="padding: 10px 20px;">Home</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="../About.html" style="padding: 10px 20px;">About</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="../Services.html" style="padding: 10px 20px;">Services</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="../Contact.html" style="padding: 10px 20px;">Contact</a>
</li></ul>
              </div>
              <div class="u-custom-menu u-nav-container-collapse">
                <div class="u-black u-container-style u-inner-container-layout u-opacity u-opacity-95 u-sidenav">
                  <div class="u-inner-container-layout u-sidenav-overflow">
                    <div class="u-menu-close"></div>
                    <ul class="u-align-center u-nav u-popupmenu-items u-unstyled u-nav-2"><li class="u-nav-item"><a class="u-button-style u-nav-link" href="./">Home</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link" href="../About.html">About</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link" href="../Services.html">Services</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link" href="../Contact.html">Contact</a>
</li></ul>
                  </div>
                </div>
                <div class="u-black u-menu-overlay u-opacity u-opacity-70"></div>
              </div>
            </nav>
          </div>
          
          
          
          
          
        </div>
      </div>
    </header>

    
    <main>
        <!-- Hero Section -->
        <section class="relative h-[80vh] flex items-center">
            <div class="absolute inset-0 z-0 bg-cover bg-center" 
                style="background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/image5.jpg');">
            </div>

            <div class="container relative z-10 mx-auto px-4 text-white">
                <h1 class="text-4xl md:text-6xl font-bold mb-4">Building with Excellence</h1>
                <p class="text-xl md:text-2xl max-w-2xl mb-8">
                    Civil engineering and general building solutions for projects of all sizes in Cape Town.
                </p>
                <a href="../Contact.html" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-md inline-flex items-center gap-2 transition-colors">
                    Get a Quote <i class="fas fa-arrow-right h-4 w-4"></i>
                </a>
            </div>
        </section>

        <!-- About Section -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-3xl mx-auto text-center">
                    <h2 class="text-3xl md:text-4xl font-bold mb-6">Civil Engineering & General Building</h2>
                    <p class="text-lg text-gray-700 mb-8">
                        We offer full-service civil engineering and general building solutions for projects of all sizes. 
                        Our team can manage your construction project from the planning and design phase all the way through to final handover. 
                        Whether you need to construct a new structure from the ground up or renovate and expand an existing building, 
                        we have the expertise to get it done right, while adhering to the highest standards.
                    </p>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl md:text-4xl font-bold mb-12 text-center">What We Do</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Service 1 -->
                    <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                        <div class="flex flex-col items-center text-center">
                            <div class="mb-4 text-primary">
                                <i class="fas fa-building text-4xl"></i>
                            </div>
                            <h3 class="text-xl font-bold mb-3">New Construction</h3>
                            <p class="text-gray-700">
                                Complete project management and construction for new homes, residential developments, commercial buildings, and community facilities. We coordinate all trades and phases – delivering turnkey projects ready for occupancy.
                            </p>
                        </div>
                    </div>
                    
                    <!-- Service 2 -->
                    <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                        <div class="flex flex-col items-center text-center">
                            <div class="mb-4 text-primary">
                                <i class="fas fa-home text-4xl"></i>
                            </div>
                            <h3 class="text-xl font-bold mb-3">Extensions & Renovations</h3>
                            <p class="text-gray-700">
                                Adding new rooms, levels, or extensions to existing structures, and upgrading or remodeling outdated spaces to meet modern standards. We work carefully within occupied homes or buildings to minimize disruption during renovations.
                            </p>
                        </div>
                    </div>
                    
                    <!-- Service 3 -->
                    <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                        <div class="flex flex-col items-center text-center">
                            <div class="mb-4 text-primary">
                                <i class="fas fa-hard-hat text-4xl"></i>
                            </div>
                            <h3 class="text-xl font-bold mb-3">Structural Work</h3>
                            <p class="text-gray-700">
                                Excavation, foundations, concrete pouring, bricklaying, and steelwork to create a solid and safe framework for your building. Our civil engineers ensure structural integrity and compliance with building codes at every step.
                            </p>
                        </div>
                    </div>
                    
                    <!-- Service 4 -->
                    <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                        <div class="flex flex-col items-center text-center">
                            <div class="mb-4 text-primary">
                                <i class="fas fa-ruler-combined text-4xl"></i>
                            </div>
                            <h3 class="text-xl font-bold mb-3">Roofing & Carpentry</h3>
                            <p class="text-gray-700">
                                Installation of robust roofing systems and timber structures (trusses, decking, formwork) that protect your property and add character. We use quality roofing materials and skilled carpenters for long-lasting, leak-free roofs.
                            </p>
                        </div>
                    </div>
                    
                    <!-- Service 5 -->
                    <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                        <div class="flex flex-col items-center text-center">
                            <div class="mb-4 text-primary">
                                <i class="fas fa-paint-roller text-4xl"></i>
                            </div>
                            <h3 class="text-xl font-bold mb-3">Finishing Oversight</h3>
                            <p class="text-gray-700">
                                Overseeing plastering, flooring, tiling, painting, and all final touches (in coordination with our finishing teams) to deliver a complete, move-in-ready building upon project completion.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-12 max-w-3xl mx-auto text-center text-gray-700">
                    <p>
                        Throughout every project, we prioritize quality control, safety, and clear communication. 
                        We conduct regular site inspections to proactively identify and resolve any issues, 
                        ensuring the project stays on schedule and within budget. We also coordinate closely with architects, 
                        engineers, and municipal authorities as needed to streamline approvals and compliance.
                    </p>
                </div>
            </div>
        </section>

        <!-- Why Choose Us Section -->
        <section class="py-16 text-white" style="background-color: #ffffff;">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl md:text-4xl font-bold mb-8 text-center text-gray-700">Why Choose Us for Your Building Project?</h2>
                <div class="max-w-3xl mx-auto">
                    <p class="text-lg mb-6 text-gray-700">
                        Choosing Jane & Peace Enterprises for your construction project means choosing a partner committed to excellence. 
                        We combine skilled craftsmanship with professional project management to ensure your build is completed efficiently 
                        and to the highest quality.
                    </p>
                    <p class="text-lg mb-6 text-gray-700">
                        Our transparent approach keeps you informed at each milestone, and our flexibility allows us to adapt to any changes 
                        or challenges. As a proudly Level 1 B-BBEE contractor, we not only deliver a great building, but also contribute to 
                        your project's empowerment objectives.
                    </p>
                    <p class="text-lg text-gray-700">
                        From luxury homes to commercial complexes, we take pride in building structures that stand the test of time and truly 
                        serve the community.
                    </p>
                </div>
            </div>
        </section>

        <!-- Projects Section -->
        <section id="projects" class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl md:text-4xl font-bold mb-12 text-center">Our Projects</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Project 1 -->
                    <div class="bg-gray-100 rounded-lg overflow-hidden shadow-md">
                        <div class="relative h-64">
                            <img src="../images/image6.png" alt="Project 1" class="w-full h-full object-cover">
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">Residential Development</h3>
                            <p class="text-gray-700">Luxury home construction showcasing our expertise in building excellence.</p>
                        </div>
                    </div>
                    
                    <!-- Project 2 -->
                    <div class="bg-gray-100 rounded-lg overflow-hidden shadow-md">
                        <div class="relative h-64">
                            <img src="../images/image7.png" alt="Project 2" class="w-full h-full object-cover">
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">Commercial Building</h3>
                            <p class="text-gray-700">Office complex construction with modern design and sustainable features.</p>
                        </div>
                    </div>
                    
                    <!-- Project 3 -->
                    <div class="bg-gray-100 rounded-lg overflow-hidden shadow-md">
                        <div class="relative h-64">
                            <img src="../images/image8.png" alt="Project 3" class="w-full h-full object-cover">
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">Structural Renovation</h3>
                            <p class="text-gray-700">Major renovation project transforming an outdated building into a modern space.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-3xl md:text-4xl font-bold mb-8 text-center">Ready to build or expand?</h2>
                    <p class="text-center text-lg mb-12">
                        Contact us today to discuss your project requirements and get a competitive quote from our building experts.
                    </p>
                    
                    <div class="bg-white rounded-lg shadow-md p-8">
                        <form method="post" action="https://public.herotofu.com/v1/ab9bb2b0-0b12-11f0-bd3e-b7254fc13091" id="contact-form" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                                    <input
                                        id="name"
                                        name="name"
                                        type="text"
                                        required
                                        placeholder="Your full name"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                    >
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                                    <input
                                        id="email"
                                        name="email"
                                        type="email"
                                        required
                                        placeholder="Your email address"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                    >
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                                    <input
                                        id="phone"
                                        name="phone"
                                        type="tel"
                                        required
                                        placeholder="Your phone number"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                    >
                                </div>
                                
                                <div class="space-y-2">
                                    <label for="projectType" class="block text-sm font-medium text-gray-700">Project Type</label>
                                    <select
                                        id="projectType"
                                        name="projectType"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                    >
                                        <option value="" disabled selected>Select project type</option>
                                        <option value="new-construction">New Construction</option>
                                        <option value="renovation">Renovation</option>
                                        <option value="extension">Extension</option>
                                        <option value="structural-work">Structural Work</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="space-y-2">
                                <label for="message" class="block text-sm font-medium text-gray-700">Project Details</label>
                                <textarea
                                    id="message"
                                    name="message"
                                    required
                                    placeholder="Tell us about your project requirements"
                                    rows="5"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                ></textarea>
                            </div>
                            
                            <button 
                                type="submit" 
                                class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-md transition-colors"
                            >
                                Send Message
                            </button>
                        </form>
                        
                        <div id="form-success" class="hidden text-center py-8">
                            <h3 class="text-2xl font-bold text-primary mb-4">Thank You!</h3>
                            <p class="text-lg">Your message has been sent successfully. We'll get back to you shortly.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">Jane & Peace Enterprises</h3>
                    <p class="mb-4">
                        We deliver end-to-end construction, renovation, and maintenance solutions for residential, commercial, and public sector clients.
                    </p>
                </div>
                
                <div>
                    <h3 class="text-xl font-bold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="../" class="hover:text-primary">Home</a>
                        </li>
                        <li>
                            <a href="../Services.html" class="hover:text-primary">Services</a>
                        </li>
                        <li>
                            <a href="../About.html" class="hover:text-primary">About</a>
                        </li>
                        <li>
                            <a href="../Contact.html" class="hover:text-primary">Contact</a>
                        </li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-xl font-bold mb-4">Contact Us</h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt h-5 w-5 mr-2 mt-0.5"></i>
                            <span>Cape Town and East London</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone h-5 w-5 mr-2"></i>
                            <span>************</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone h-5 w-5 mr-2"></i>
                            <span>************</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-12 pt-8 text-center">
                <p>&copy; <span id="current-year"></span> Jane & Peace Enterprises. All rights reserved.</p>
            </div>
        </div>

        <div class="u-clearfix u-sheet u-sheet-1">
          <div class="u-align-center u-clearfix u-custom-html u-expanded-width u-custom-html-1">
            <div id="google_translate_element"></div>
            <script type="text/javascript"> function googleTranslateElementInit() {
    new google.translate.TranslateElement({pageLanguage: 'en'}, 'google_translate_element');
  } </script>
            <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
          </div>
        </div>
    </footer>

    <style>
        /* Custom styles beyond Tailwind */

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
:focus {
  outline: 2px solid #1e5b98;
  outline-offset: 2px;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

/* Custom form styles */
input:focus,
textarea:focus,
select:focus {
  border-color: #1e5b98;
}

/* Custom button hover effect */
.btn-hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Mobile menu transition */
#mobile-menu {
  transition: all 0.3s ease-in-out;
  max-height: 0;
  overflow: hidden;
}

#mobile-menu.open {
  max-height: 300px;
}

/* Service card hover effect */
.service-card:hover {
  transform: translateY(-5px);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #1e5b98;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #154a7d;
}


    </style>

    <!-- JavaScript -->
     <script>
        document.addEventListener("DOMContentLoaded", () => {
  // Mobile menu toggle
  const menuToggle = document.getElementById("menu-toggle")
  const mobileMenu = document.getElementById("mobile-menu")
  const menuIcon = menuToggle.querySelector("i")

  menuToggle.addEventListener("click", () => {
    mobileMenu.classList.toggle("hidden")

    // Toggle icon between bars and X
    if (menuIcon.classList.contains("fa-bars")) {
      menuIcon.classList.remove("fa-bars")
      menuIcon.classList.add("fa-times")
    } else {
      menuIcon.classList.remove("fa-times")
      menuIcon.classList.add("fa-bars")
    }
  })

  // Close mobile menu when clicking on a link
  const mobileLinks = mobileMenu.querySelectorAll("a")
  mobileLinks.forEach((link) => {
    link.addEventListener("click", () => {
      mobileMenu.classList.add("hidden")
      menuIcon.classList.remove("fa-times")
      menuIcon.classList.add("fa-bars")
    })
  })

  // Contact form submission
  const contactForm = document.getElementById("contact-form")
  const formSuccess = document.getElementById("form-success")

  if (contactForm) {
    contactForm.addEventListener("submit", (e) => {
      e.preventDefault()

      // Get form data
      const formData = new FormData(contactForm)
      const formDataObj = {}
      formData.forEach((value, key) => {
        formDataObj[key] = value
      })

      // Simulate form submission with a delay
      const submitButton = contactForm.querySelector('button[type="submit"]')
      submitButton.disabled = true
      submitButton.textContent = "Sending..."

      setTimeout(() => {
        // Hide form and show success message
        contactForm.style.display = "none"
        formSuccess.classList.remove("hidden")
        formSuccess.classList.add("fade-in")

        // Reset form
        contactForm.reset()
        submitButton.disabled = false
        submitButton.textContent = "Send Message"

        // Hide success message after 5 seconds and show form again
        setTimeout(() => {
          formSuccess.classList.add("hidden")
          contactForm.style.display = "block"
        }, 5000)
      }, 1500)
    })
  }

  // Smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault()

      const targetId = this.getAttribute("href")
      if (targetId === "#") return

      const targetElement = document.querySelector(targetId)
      if (targetElement) {
        // Offset for fixed header
        const headerHeight = document.querySelector("header").offsetHeight
        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight

        window.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        })
      }
    })
  })

  // Set current year in footer
  document.getElementById("current-year").textContent = new Date().getFullYear()

  // Animate elements on scroll
  const animateOnScroll = () => {
    const elements = document.querySelectorAll(".animate-on-scroll")

    elements.forEach((element) => {
      const elementPosition = element.getBoundingClientRect().top
      const windowHeight = window.innerHeight

      if (elementPosition < windowHeight - 100) {
        element.classList.add("fade-in")
      }
    })
  }

  // Add animate-on-scroll class to elements that should animate
  document.querySelectorAll("section > div > h2").forEach((heading) => {
    heading.classList.add("animate-on-scroll")
  })

  // Initial check and add scroll event listener
  animateOnScroll()
  window.addEventListener("scroll", animateOnScroll)
})


     </script>
  
</body></html>