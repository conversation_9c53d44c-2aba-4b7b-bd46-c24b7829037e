.u-section-1 .u-sheet-1 {
  min-height: 973px;
}

.u-section-1 .u-text-1 {
  width: 318px;
  margin: 30px auto 0;
}

.u-section-1 .u-search-1 {
  height: auto;
  width: 660px;
  min-height: 38px;
  margin: 20px auto 0;
}

.u-section-1 .u-blog-1 {
  display: none;
}

.u-section-1 .u-repeater-1 {
  grid-template-columns: repeat(1, 100%);
  min-height: 584px;
  grid-auto-columns: 100%;
  grid-gap: 10px;
}

.u-section-1 .u-container-layout-1 {
  padding: 30px;
}

.u-section-1 .u-text-2 {
  margin: 0;
}

.u-section-1 .u-text-3 {
  margin: 20px 0 0;
}

.u-section-1 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1 .u-text-4 {
  margin: 0;
}

.u-section-1 .u-text-5 {
  margin: 20px 0 0;
}

.u-section-1 .u-container-layout-3 {
  padding: 30px;
}

.u-section-1 .u-text-6 {
  margin: 0;
}

.u-section-1 .u-text-7 {
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-blog-1 {
    min-height: 768px;
    height: auto;
    margin-top: 40px;
    margin-bottom: 60px;
  }

  .u-section-1 .u-repeater-1 {
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-search-1 {
    width: 540px;
  }

  .u-section-1 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-search-1 {
    width: 340px;
  }
}