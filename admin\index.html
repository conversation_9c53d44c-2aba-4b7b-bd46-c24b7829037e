<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title><PERSON> and Jane</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0; /* Changed to 0 to accommodate loading screen */
      background-color: #f4f4f4;
    }
    .container {
      max-width: 800px;
      margin: auto;
      background: white;
      padding: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    h1, h2 {
      color: #333;
    }
    .section {
      margin-bottom: 20px;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 5px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input, select, textarea {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    textarea {
      resize: vertical;
      min-height: 80px;
    }
    input[readonly] {
      background-color: #e9e9e9;
      cursor: not-allowed;
    }
    button {
      background-color: #4a90e2;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
    }
    button:hover {
      background-color: #81b0e6;
    }
    .button-group {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      table-layout: auto;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
      word-wrap: break-word;
    }
    th {
      background-color: #f2f2f2;
    }
    .loader {
      border: 4px solid #f3f3f3; /* Light grey */
      border-top: 4px solid #3498db; /* Blue */
      border-radius: 50%;
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
      display: none;
      margin-left: 10px;
      vertical-align: middle;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .table-responsive {
      width: 100%;
      overflow-x: auto;
    }
    @media (max-width: 600px) {
      body {
        padding: 10px;
      }
      .container {
        padding: 15px;
      }
      button {
        width: 100%;
        padding: 12px 0;
        font-size: 18px;
        margin-right: 0;
      }
      th, td {
        padding: 10px;
        font-size: 14px;
      }
      .loader {
        width: 25px;
        height: 25px;
      }
      .section {
        padding: 10px;
      }
      .button-group {
        flex-direction: column;
        align-items: stretch;
      }
    }
    .hidden {
      display: none;
    }
    .remove-button {
      background-color: #ff4d4d;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
    }
    .remove-button:hover {
      background-color: #e60000;
    }
    /* Loading Screen Styles */
    #loadingScreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.9);
      z-index: 9999;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    #loadingScreen .spinner {
      border: 8px solid #f3f3f3; /* Light grey */
      border-top: 8px solid #3498db; /* Blue */
      border-radius: 50%;
      width: 60px;
      height: 60px;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    #loadingScreen p {
      font-size: 18px;
      color: #333;
    }
    /* Toast Container and Toast Styles */
    .toast-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000; /* on top of loading screen */
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    .toast {
      background-color: #333;
      color: #fff;
      padding: 10px 15px;
      border-radius: 5px;
      opacity: 0.95;
      font-size: 14px;
      animation: fadeInOut 3s forwards;
    }
    .toast.success {
      background-color: #28a745;
    }
    .toast.error {
      background-color: #dc3545;
    }
    .toast.info {
      background-color: #007bff;
    }
    @keyframes fadeInOut {
      0% { opacity: 0; }
      10% { opacity: 0.95; }
      90% { opacity: 0.95; }
      100% { opacity: 0; }
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loadingScreen">
    <div class="spinner"></div>
    <p>Loading products, please wait...</p>
  </div>

  <!-- Toast Container (for all messages) -->
  <div class="toast-container" id="toastContainer"></div>

  <div class="container">
    <h1>Peace and Jane - QIR Generator</h1>
    
    <!-- Customer Information Section -->
    <div class="section">
      <h2>Customer Information</h2>
      <label for="customerName">Name:</label>
      <input type="text" id="customerName" required>
      
      <label for="customerEmail">Email:</label>
      <input type="email" id="customerEmail">
      
      <label for="customerAddress">Address:</label>
      <input type="text" id="customerAddress">
      
      <label for="customerPhone">Phone:</label>
      <input type="tel" id="customerPhone">
    </div>
    
    <!-- Shipping Information Section -->
    <div class="section">
      <h2>Shipping Information</h2>
      <div style="display: flex; gap: 10px; align-items: center;">
        <label for="sameAsBilling">Ship to the same address as billing</label>
        <input type="checkbox" id="sameAsBilling" checked>
      </div>
      
      <div id="shippingInfo" class="hidden">
        <label for="shippingName">Name:</label>
        <input type="text" id="shippingName">
        
        <label for="shippingEmail">Email:</label>
        <input type="email" id="shippingEmail">
        
        <label for="shippingAddress">Address:</label>
        <input type="text" id="shippingAddress">
        
        <label for="shippingPhone">Phone:</label>
        <input type="tel" id="shippingPhone">
      </div>
    </div>
    
    <!-- Add Products Section -->
    <div class="section">
      <h2>Add Products</h2>
      <label for="productSearch">Product Name:</label>
      <input type="text" id="productSearch" placeholder="Enter product name">
      
      <label for="quantity">Quantity:</label>
      <input type="number" id="quantity" value="1" min="1">
      
      <label for="price">Price (R):</label>
      <input type="number" id="price" value="0.00" min="0" step="0.01">
      
      <button onclick="addSelectedProduct()">Add Product</button>
    </div>
    
    <!-- Selected Products and Totals Section -->
    <div class="section">
      <h2>Selected Products</h2>
      <div class="table-responsive">
        <table id="selectedProductsTable">
          <thead></thead>
          <tbody id="selectedProductsBody"></tbody>
        </table>
      </div>

      <div style="display: flex; align-items: center;">
        <label for="Tax">Add Tax</label>
        <input type="checkbox" id="Tax" checked>
      </div>

      <!-- Display the calculated values -->
      <p>Product Amount (Subtotal): R<span id="subtotalAmount">0.00</span></p>
      <p>Tax (15%): R<span id="taxAmount">0.00</span></p>
      <p>Total Amount: R<span id="totalAmount">0.00</span></p>

      <!-- Comments Section -->
      <div class="section" style="border:none; padding:0;">
        <label for="paymentMethod">Payment Method:</label>
        <select id="paymentMethod">
          <option value="Cash">Cash</option>
          <option value="EFT">EFT</option>
        </select>
      </div>

      <!-- Added Comments Box -->
      <div class="section" style="border:none; padding:0;">
        <label for="comments">Additional Comments:</label>
        <textarea id="comments" placeholder="Enter any additional comments here..."></textarea>
      </div>

      <div class="button-group">
        <button onclick="generateReceipt()">Generate Receipt</button>
        <button onclick="generateQuotation()">Generate Quotation</button>
        <button onclick="generateInvoice()">Generate Invoice</button>
      </div>
    </div>
  </div>

  <script>
    let selectedProducts = [];
    let newProductCounter = 1; // Counter for new products
  
    const CURRENCY_SYMBOL = 'R';
    const TAX_RATE = 0.15;
  
    const productSearchInput = document.getElementById('productSearch');
    // roomInput and roomLabel remain in case you want to extend functionality later
    const sameAsBillingCheckbox = document.getElementById('sameAsBilling');
    const shippingInfoDiv = document.getElementById('shippingInfo');
    const paymentMethodSelect = document.getElementById('paymentMethod');
    const taxCheckbox = document.getElementById('Tax');
    const taxAmountSpan = document.getElementById('taxAmount');
    const subtotalAmountSpan = document.getElementById('subtotalAmount');
    const totalAmountSpan = document.getElementById('totalAmount');
    const selectedProductsTable = document.getElementById('selectedProductsTable');
    const commentsTextarea = document.getElementById('comments');
    const loadingScreen = document.getElementById('loadingScreen');
  
    // Fixed company information; no need to choose a company.
    const companies = {
      company1: {
        name: "JANE AND PEACE ENTERPRISES (PTY)LTD",
        bankingInformation: `
          First National Bank<br>
          GOLD BUSINESS ACCOUNT<br>
          Account :  ***********<br>
          Branch code 201909<br>
          Swift code FIRNZAJJ
        `
      }
    };
  
    /**
     * Display a non-blocking toast message in the top-right corner.
     * @param {string} message - The message to display
     * @param {string} [type='info'] - 'success', 'error', or 'info'
     */
    function showToastMessage(message, type = 'info') {
      const toastContainer = document.getElementById('toastContainer');
      const toast = document.createElement('div');
      toast.classList.add('toast', type);
      toast.textContent = message;
      toastContainer.appendChild(toast);
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }
  
    function setCookie(name, value, days) {
      const d = new Date();
      d.setTime(d.getTime() + (days * 24 * 60 * 60 * 1000));
      const expires = "expires=" + d.toUTCString();
      document.cookie = name + "=" + encodeURIComponent(value) + ";" + expires + ";path=/";
    }
  
    function getCookie(name) {
      const cname = name + "=";
      const decodedCookie = decodeURIComponent(document.cookie);
      const ca = decodedCookie.split(';');
      for (let i = 0; i < ca.length; i++) {
        let c = ca[i].trim();
        if (c.indexOf(cname) === 0) {
          return c.substring(cname.length, c.length);
        }
      }
      return "";
    }
  
    // Add product using the manually entered details.
    function addSelectedProduct() {
      const productName = productSearchInput.value.trim();
      const quantity = parseInt(document.getElementById('quantity').value);
      const price = parseFloat(document.getElementById('price').value).toFixed(2);
  
      if (productName === '' || isNaN(quantity) || isNaN(price)) {
        showToastMessage('Please enter valid product details.', 'error');
        return;
      }
      if (quantity <= 0) {
        showToastMessage('Quantity must be at least 1.', 'error');
        return;
      }
      if (price < 0) {
        showToastMessage('Price cannot be negative.', 'error');
        return;
      }
  
      let taxPerProduct = 0.00;
      if (taxCheckbox.checked) {
        taxPerProduct = parseFloat((price * TAX_RATE).toFixed(2));
      }
  
      const item_code = `NEW-${newProductCounter++}`;
      selectedProducts.push({
        item_code: item_code,
        name: productName,
        quantity: quantity,
        price: parseFloat(price),
        tax_per_product: taxPerProduct,
        is_new: true
      });
  
      updateSelectedProductsTable();
      productSearchInput.value = '';
      document.getElementById('quantity').value = '1';
      document.getElementById('price').value = '0.00';
    }
  
    function updateSelectedProductsTable() {
  const tbody = document.getElementById('selectedProductsBody');
  tbody.innerHTML = '';
  updateTableHeader();

  selectedProducts.forEach((product, index) => {
    const row = tbody.insertRow();

    // Product name
    const nameCell = row.insertCell();
    nameCell.textContent = product.name;
    if (product.is_new) {
      const newLabel = document.createElement('span');
      newLabel.textContent = ' (New)';
      newLabel.style.color = '#ff9900';
      newLabel.style.fontWeight = 'bold';
      nameCell.appendChild(newLabel);
    }

    // Quantity
    const qtyCell = row.insertCell();
    qtyCell.textContent = product.quantity;

    if (taxCheckbox.checked) {
      // Use base price for unit price, then compute tax per unit and total per product.
      const unitPrice = product.price;
      const taxAmount = product.price * TAX_RATE;
      const lineTotal = (product.price + taxAmount) * product.quantity;

      const unitPriceCell = row.insertCell();
      unitPriceCell.textContent = `${CURRENCY_SYMBOL}${unitPrice.toFixed(2)}`;

      const taxCell = row.insertCell();
      taxCell.textContent = `${CURRENCY_SYMBOL}${taxAmount.toFixed(2)}`;

      const totalCell = row.insertCell();
      totalCell.textContent = `${CURRENCY_SYMBOL}${lineTotal.toFixed(2)}`;

      const actionCell = row.insertCell();
      const removeBtn = document.createElement('button');
      removeBtn.textContent = 'Remove';
      removeBtn.classList.add('remove-button');
      removeBtn.onclick = () => removeProduct(index);
      actionCell.appendChild(removeBtn);
    } else {
      // Without tax, use the price as-is.
      const lineTotal = product.price * product.quantity;

      const unitPriceCell = row.insertCell();
      unitPriceCell.textContent = `${CURRENCY_SYMBOL}${product.price.toFixed(2)}`;

      const totalCell = row.insertCell();
      totalCell.textContent = `${CURRENCY_SYMBOL}${lineTotal.toFixed(2)}`;

      const actionCell = row.insertCell();
      const removeBtn = document.createElement('button');
      removeBtn.textContent = 'Remove';
      removeBtn.classList.add('remove-button');
      removeBtn.onclick = () => removeProduct(index);
      actionCell.appendChild(removeBtn);
    }
  });

  calculateAndDisplayTotal();
}

function calculateAndDisplayTotal() {
  let subtotal = 0;
  let tax = 0;
  let total = 0;

  // Calculate the subtotal (base prices) and tax per product.
  selectedProducts.forEach(product => {
    subtotal += product.price * product.quantity;
    if (taxCheckbox.checked) {
      tax += product.price * TAX_RATE * product.quantity;
    }
  });

  total = subtotal + tax;

  subtotalAmountSpan.textContent = subtotal.toFixed(2);
  taxAmountSpan.textContent = tax.toFixed(2);
  totalAmountSpan.textContent = total.toFixed(2);
}

  
    function updateTableHeader() {
      const thead = selectedProductsTable.querySelector('thead');
      thead.innerHTML = '';
      const headerRow = thead.insertRow();
      headerRow.insertCell().textContent = 'Product';
      headerRow.insertCell().textContent = 'Quantity';
      headerRow.insertCell().textContent = 'Unit Price (R)';
  
      if (taxCheckbox.checked) {
        headerRow.insertCell().textContent = 'Tax per Unit (R)';
        headerRow.insertCell().textContent = 'Total (R)';
        headerRow.insertCell().textContent = 'Action';
      } else {
        headerRow.insertCell().textContent = 'Total (R)';
        headerRow.insertCell().textContent = 'Action';
      }
    }
  
    function removeProduct(index) {
      const removedProduct = selectedProducts.splice(index, 1)[0];
      updateSelectedProductsTable();
      showToastMessage(`Removed ${removedProduct.name} from the selection.`, 'info');
    }
  
    // Generate Receipt using the fixed company (Peace And Jane)
    function generateReceipt() {
      const selectedCompany = companies.company1;
  
      const billingInfo = {
        name: document.getElementById('customerName').value.trim(),
        email: document.getElementById('customerEmail').value.trim(),
        address: document.getElementById('customerAddress').value.trim(),
        phone: document.getElementById('customerPhone').value.trim()
      };
  
      let shippingInfo = null;
      if (!sameAsBillingCheckbox.checked) {
        shippingInfo = {
          name: document.getElementById('shippingName').value.trim(),
          email: document.getElementById('shippingEmail').value.trim(),
          address: document.getElementById('shippingAddress').value.trim(),
          phone: document.getElementById('shippingPhone').value.trim()
        };
        if (!shippingInfo.name || !shippingInfo.email || !shippingInfo.address || !shippingInfo.phone) {
          showToastMessage('Please fill in all shipping information fields or select "Ship to the same address as billing".', 'error');
          return;
        }
      }
  
      if (!billingInfo.name || !billingInfo.email || !billingInfo.address || !billingInfo.phone) {
        showToastMessage('Please fill in all customer information fields.', 'error');
        return;
      }
  
      if (selectedProducts.length === 0) {
        showToastMessage('No products selected.', 'error');
        return;
      }
  
      const paymentMethod = paymentMethodSelect.value;
      const comments = commentsTextarea.value.trim();
      const customerInfo = {
        billing: billingInfo,
        shipping: sameAsBillingCheckbox.checked ? null : shippingInfo,
        paymentMethod: paymentMethod,
        comments: comments
      };
  
      setCookie('customerInfo', JSON.stringify(customerInfo), 1);
      setCookie('selectedProducts', JSON.stringify(selectedProducts), 1);
      setCookie('subtotalAmount', JSON.stringify(subtotalAmountSpan.textContent), 1);
      setCookie('taxAmount', JSON.stringify(taxAmountSpan.textContent), 1);
      setCookie('totalAmount', JSON.stringify(totalAmountSpan.textContent), 1);
      setCookie('selectedCompany', JSON.stringify(selectedCompany), 1);
  
      window.location.href = 'receipt.html';
    }
  
    // Generate Quotation using the fixed company (Peace And Jane)
    function generateQuotation() {
      const selectedCompany = companies.company1;
  
      const billingInfo = {
        name: document.getElementById('customerName').value.trim(),
        email: document.getElementById('customerEmail').value.trim(),
        address: document.getElementById('customerAddress').value.trim(),
        phone: document.getElementById('customerPhone').value.trim()
      };
  
      let shippingInfo = null;
      if (!sameAsBillingCheckbox.checked) {
        shippingInfo = {
          name: document.getElementById('shippingName').value.trim(),
          email: document.getElementById('shippingEmail').value.trim(),
          address: document.getElementById('shippingAddress').value.trim(),
          phone: document.getElementById('shippingPhone').value.trim()
        };
        if (!shippingInfo.name || !shippingInfo.email || !shippingInfo.address || !shippingInfo.phone) {
          showToastMessage('Please fill in all shipping information fields or select "Ship to the same address as billing".', 'error');
          return;
        }
      }
  
      if (!billingInfo.name || !billingInfo.email || !billingInfo.address || !billingInfo.phone) {
        showToastMessage('Please fill in all customer information fields.', 'error');
        return;
      }
  
      if (selectedProducts.length === 0) {
        showToastMessage('No products selected.', 'error');
        return;
      }
  
      const comments = commentsTextarea.value.trim();
      const customerInfo = {
        billing: billingInfo,
        shipping: sameAsBillingCheckbox.checked ? null : shippingInfo,
        paymentMethod: 'N/A',
        comments: comments
      };
  
      setCookie('customerInfo', JSON.stringify(customerInfo), 1);
      setCookie('selectedProducts', JSON.stringify(selectedProducts), 1);
      setCookie('subtotalAmount', JSON.stringify(subtotalAmountSpan.textContent), 1);
      setCookie('taxAmount', JSON.stringify(taxAmountSpan.textContent), 1);
      setCookie('totalAmount', JSON.stringify(totalAmountSpan.textContent), 1);
      setCookie('selectedCompany', JSON.stringify(selectedCompany), 1);
  
      window.location.href = 'quotation.html';
    }
  
    // Generate Invoice using the fixed company (Peace And Jane)
    function generateInvoice() {
      const selectedCompany = companies.company1;
  
      const billingInfo = {
        name: document.getElementById('customerName').value.trim(),
        email: document.getElementById('customerEmail').value.trim(),
        address: document.getElementById('customerAddress').value.trim(),
        phone: document.getElementById('customerPhone').value.trim()
      };
  
      let shippingInfo = null;
      if (!sameAsBillingCheckbox.checked) {
        shippingInfo = {
          name: document.getElementById('shippingName').value.trim(),
          email: document.getElementById('shippingEmail').value.trim(),
          address: document.getElementById('shippingAddress').value.trim(),
          phone: document.getElementById('shippingPhone').value.trim()
        };
        if (!shippingInfo.name || !shippingInfo.email || !shippingInfo.address || !shippingInfo.phone) {
          showToastMessage('Please fill in all shipping information fields or select "Ship to the same address as billing".', 'error');
          return;
        }
      }
  
      if (!billingInfo.name || !billingInfo.email || !billingInfo.address || !billingInfo.phone) {
        showToastMessage('Please fill in all customer information fields.', 'error');
        return;
      }
  
      if (selectedProducts.length === 0) {
        showToastMessage('No products selected.', 'error');
        return;
      }
  
      const paymentMethod = paymentMethodSelect.value;
      const comments = commentsTextarea.value.trim();
      const customerInfo = {
        billing: billingInfo,
        shipping: sameAsBillingCheckbox.checked ? null : shippingInfo,
        paymentMethod: paymentMethod,
        comments: comments
      };
  
      setCookie('customerInfo', JSON.stringify(customerInfo), 1);
      setCookie('selectedProducts', JSON.stringify(selectedProducts), 1);
      setCookie('subtotalAmount', JSON.stringify(subtotalAmountSpan.textContent), 1);
      setCookie('taxAmount', JSON.stringify(taxAmountSpan.textContent), 1);
      setCookie('totalAmount', JSON.stringify(totalAmountSpan.textContent), 1);
      setCookie('selectedCompany', JSON.stringify(selectedCompany), 1);
  
      window.location.href = 'invoice.html';
    }
  
    function toggleShippingInfo() {
      if (sameAsBillingCheckbox.checked) {
        shippingInfoDiv.classList.add('hidden');
      } else {
        shippingInfoDiv.classList.remove('hidden');
      }
    }
  
    sameAsBillingCheckbox.addEventListener('change', toggleShippingInfo);
    taxCheckbox.addEventListener('change', () => {
      updateSelectedProductsTable();
    });
  
    window.onload = function() {
      loadingScreen.style.display = 'none';
      updateSelectedProductsTable();
    };
  </script>
  
</body>
</html>
