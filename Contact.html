<!DOCTYPE html>
<html style="font-size: 16px;" lang="en"><head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="utf-8">
    <meta name="keywords" content="Contact Us">
    <meta name="description" content="">
    <title>Contact</title>
    <link rel="stylesheet" href="nicepage.css" media="screen">
<link rel="stylesheet" href="Contact.css" media="screen">
    <script class="u-script" type="text/javascript" src="jquery.js" defer=""></script>
    <script class="u-script" type="text/javascript" src="nicepage.js" defer=""></script>
    <meta name="generator" content="Nicepage 7.6.5, nicepage.com">
    <meta name="referrer" content="origin">
    
    
    
    
    <link id="u-theme-google-font" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:100,100i,300,300i,400,400i,500,500i,700,700i,900,900i|Open+Sans:300,300i,400,400i,500,500i,600,600i,700,700i,800,800i">
    <link id="u-page-google-font" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,500,500i,600,600i,700,700i,800,800i">
    <script type="application/ld+json">{
		"@context": "http://schema.org",
		"@type": "Organization",
		"name": "",
		"logo": "images/logo.png",
		"sameAs": [
				"https://facebook.com/name",
				"https://twitter.com/name",
				"https://www.instagram.com/name"
		]
}</script>


<script src="https://cdn.tailwindcss.com"></script>
    
<!-- Custom Tailwind Config -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    primary: '#1e5b98',
                    secondary: '#f59e0b',
                }
            }
        }
    }
</script>


    <meta name="theme-color" content="#478ac9">
    <meta name="twitter:site" content="@">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Contact">
    <meta name="twitter:description" content="">
    <meta property="og:title" content="Contact">
    <meta property="og:type" content="website">
  <meta data-intl-tel-input-cdn-path="intlTelInput/"></head>
  <body data-path-to-root="./" data-include-products="false" class="u-body u-xl-mode" data-lang="en"><header class=" u-clearfix u-header u-section-row-container" id="header" data-animation-name="" data-animation-duration="0" data-animation-delay="0" data-animation-direction=""><div class="u-section-rows">
    <div class="u-hidden-xs u-section-row" id="sec-71fa">
      <div class="u-clearfix u-sheet u-valign-middle-lg u-valign-middle-md u-valign-middle-sm u-sheet-1">
        <a href="#" class="u-image u-logo u-image-1" data-image-width="602" data-image-height="631">
          <img src="../images/logo.png" class="u-logo-image u-logo-image-1" data-image-width="80">
        </a>
        <div class="u-social-icons u-social-icons-1">
          <a class="u-social-url" title="facebook" target="_blank" href="https://www.facebook.com/share/1AN9a562UX/?mibextid=qi2Omg">
            <span class="u-icon u-social-facebook u-social-icon u-icon-1"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 112 112" style=""><use xlink:href="#svg-7e28"></use></svg><svg class="u-svg-content" viewBox="0 0 112 112" x="0" y="0" id="svg-7e28"><circle fill="currentColor" cx="56.1" cy="56.1" r="55"></circle><path fill="#FFFFFF" d="M73.5,31.6h-9.1c-1.4,0-3.6,0.8-3.6,3.9v8.5h12.6L72,58.3H60.8v40.8H43.9V58.3h-8V43.9h8v-9.2
  c0-6.7,3.1-17,17-17h12.5v13.9H73.5z"></path></svg></span>
          </a>
          <a class="u-social-url" title="twitter" target="_blank" href="https://www.tiktok.com/@janeandpeace?_t=ZM-8v9UA47zw7L&_r=1">
            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="35" height="35" viewBox="0 0 50 50">
                <path d="M41,4H9C6.243,4,4,6.243,4,9v32c0,2.757,2.243,5,5,5h32c2.757,0,5-2.243,5-5V9C46,6.243,43.757,4,41,4z M37.006,22.323 c-0.227,0.021-0.457,0.035-0.69,0.035c-2.623,0-4.928-1.349-6.269-3.388c0,5.349,0,11.435,0,11.537c0,4.709-3.818,8.527-8.527,8.527 s-8.527-3.818-8.527-8.527s3.818-8.527,8.527-8.527c0.178,0,0.352,0.016,0.527,0.027v4.202c-0.175-0.021-0.347-0.053-0.527-0.053 c-2.404,0-4.352,1.948-4.352,4.352s1.948,4.352,4.352,4.352s4.527-1.894,4.527-4.298c0-0.095,0.042-19.594,0.042-19.594h4.016 c0.378,3.591,3.277,6.425,6.901,6.685V22.323z"></path>
            </svg>
          </a>
          <a class="u-social-url" title="instagram" target="_blank" href="https://www.instagram.com/p/DH4H_NWIZzT/?igsh=c21tMjVtMXpmZWM=">
            <span class="u-icon u-social-icon u-social-instagram u-icon-3"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 112 112" style=""><use xlink:href="#svg-d54d"></use></svg><svg class="u-svg-content" viewBox="0 0 112 112" x="0" y="0" id="svg-d54d"><circle fill="currentColor" cx="56.1" cy="56.1" r="55"></circle><path fill="#FFFFFF" d="M55.9,38.2c-9.9,0-17.9,8-17.9,17.9C38,66,46,74,55.9,74c9.9,0,17.9-8,17.9-17.9C73.8,46.2,65.8,38.2,55.9,38.2
  z M55.9,66.4c-5.7,0-10.3-4.6-10.3-10.3c-0.1-5.7,4.6-10.3,10.3-10.3c5.7,0,10.3,4.6,10.3,10.3C66.2,61.8,61.6,66.4,55.9,66.4z"></path><path fill="#FFFFFF" d="M74.3,33.5c-2.3,0-4.2,1.9-4.2,4.2s1.9,4.2,4.2,4.2s4.2-1.9,4.2-4.2S76.6,33.5,74.3,33.5z"></path><path fill="#FFFFFF" d="M73.1,21.3H38.6c-9.7,0-17.5,7.9-17.5,17.5v34.5c0,9.7,7.9,17.6,17.5,17.6h34.5c9.7,0,17.5-7.9,17.5-17.5V38.8
  C90.6,29.1,82.7,21.3,73.1,21.3z M83,73.3c0,5.5-4.5,9.9-9.9,9.9H38.6c-5.5,0-9.9-4.5-9.9-9.9V38.8c0-5.5,4.5-9.9,9.9-9.9h34.5
  c5.5,0,9.9,4.5,9.9,9.9V73.3z"></path></svg></span>
          </a>
        </div>
      </div>
    </div>
        <div class="u-grey-5 u-section-row u-section-row-2" id="sec-13b1">
          <div class="u-clearfix u-sheet u-valign-middle-lg u-valign-middle-md u-valign-middle-sm u-valign-middle-xl u-sheet-2">
            <a href="#" class="u-hidden-md u-hidden-sm u-hidden-xl u-image u-logo u-image-2" data-image-width="602" data-image-height="631">
              <img src="images/logo.png" class="u-logo-image u-logo-image-2" data-image-width="80">
            </a>
            <nav class="u-menu u-menu-one-level u-offcanvas u-menu-1">
              <div class="menu-collapse" style="font-size: 1rem; letter-spacing: 0px; font-weight: 700; text-transform: uppercase;">
                <a class="u-button-style u-custom-active-border-color u-custom-border u-custom-border-color u-custom-borders u-custom-hover-border-color u-custom-left-right-menu-spacing u-custom-text-active-color u-custom-text-color u-custom-text-hover-color u-custom-top-bottom-menu-spacing u-hamburger-link u-nav-link" href="#">
                  <svg class="u-svg-link" viewBox="0 0 24 24"><use xlink:href="#menu-hamburger"></use></svg>
                  <svg class="u-svg-content" version="1.1" id="menu-hamburger" viewBox="0 0 16 16" x="0px" y="0px" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg"><g><rect y="1" width="16" height="2"></rect><rect y="7" width="16" height="2"></rect><rect y="13" width="16" height="2"></rect>
</g></svg>
                </a>
              </div>
              <div class="u-custom-menu u-nav-container">
                <ul class="u-nav u-unstyled u-nav-1"><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="./" style="padding: 10px 20px;">Home</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="About.html" style="padding: 10px 20px;">About</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="Services.html" style="padding: 10px 20px;">Services</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="Contact.html" style="padding: 10px 20px;">Contact</a>
</li></ul>
              </div>
              <div class="u-custom-menu u-nav-container-collapse">
                <div class="u-black u-container-style u-inner-container-layout u-opacity u-opacity-95 u-sidenav">
                  <div class="u-inner-container-layout u-sidenav-overflow">
                    <div class="u-menu-close"></div>
                    <ul class="u-align-center u-nav u-popupmenu-items u-unstyled u-nav-2"><li class="u-nav-item"><a class="u-button-style u-nav-link" href="./">Home</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link" href="About.html">About</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link" href="Services.html">Services</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link" href="Contact.html">Contact</a>
</li></ul>
                  </div>
                </div>
                <div class="u-black u-menu-overlay u-opacity u-opacity-70"></div>
              </div>
            </nav>
          </div>
          
          
          
          
          
        </div>
      </div></header>
    <section class="u-align-center u-clearfix u-valign-bottom u-section-1" id="sec-d7fc">
      <h2 class="u-custom-font u-text u-text-font u-text-1" spellcheck="false">Contact Us</h2>
      <p class="u-align-center u-text u-text-grey-75 u-text-2" spellcheck="false"><b>Get in touch with Jane &amp; Peace Enterprises for all your construction and service needs. Contact us via phone, email, or our online form to request a free quote, inquire about services, or chat with our live support.</b>
      </p>
      <div class="data-layout-selected u-clearfix u-expanded-width u-layout-wrap u-layout-wrap-1">
        <div class="u-gutter-0 u-layout">
          <div class="u-layout-col">
            <div class="u-size-30">
              <div class="u-layout-row">
                <div class="u-align-left u-container-style u-layout-cell u-palette-4-base u-size-20 u-layout-cell-1">
                  <div class="u-container-layout u-valign-top u-container-layout-1">
                    <span class="u-icon u-icon-circle u-text-palette-4-light-2 u-icon-1"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 511.999 511.999" style=""><use xlink:href="#svg-82ec"></use></svg><svg class="u-svg-content" viewBox="0 0 511.999 511.999" x="0px" y="0px" id="svg-82ec" style="enable-background:new 0 0 511.999 511.999;"><g><g><g><path d="M267.998,61.092c0.337,0,0.665-0.002,1-0.002c48.74,0,94.412,18.844,128.686,53.118     c34.557,34.557,53.459,80.618,53.225,129.697c-0.052,11.045,8.859,20.042,19.905,20.095c0.033,0,0.064,0,0.097,0     c10.999,0,19.944-8.892,19.997-19.905c0.285-59.837-22.778-116.009-64.94-158.172C384.134,44.088,328.43,21.09,269.004,21.09     c-0.365,0-0.733,0.001-1.099,0.002c-11.046,0.053-19.957,9.05-19.905,20.095C248.053,52.2,256.997,61.092,267.998,61.092z"></path><path d="M511.949,412.553c-0.714-19.805-8.964-38.287-23.229-52.042c-27.931-26.933-51.332-42.481-73.646-48.929     c-30.75-8.887-59.226-0.805-84.641,24.016c-0.038,0.037-0.077,0.075-0.115,0.113l-27.066,26.865     c-16.811-9.444-49.491-30.227-85.234-65.971l-2.632-2.63c-35.508-35.507-56.423-68.373-65.939-85.253l26.839-27.041     c0.038-0.038,0.075-0.076,0.113-0.115c24.821-25.414,32.902-53.892,24.016-84.64c-6.448-22.313-21.995-45.715-48.929-73.646     C137.731,9.015,119.249,0.765,99.446,0.051C79.647-0.662,60.614,6.232,45.865,19.472l-0.574,0.516     c-0.267,0.239-0.527,0.486-0.78,0.739c-29.36,29.358-44.75,70.46-44.508,118.861c0.41,82.22,45.599,176.249,120.879,251.528     c0.063,0.063,0.125,0.124,0.188,0.186c14.152,14.132,30.22,28.116,47.762,41.567c8.765,6.721,21.319,5.063,28.041-3.702     c6.721-8.766,5.064-21.32-3.702-28.041c-16.236-12.448-31.041-25.333-44.004-38.296c-0.062-0.062-0.124-0.124-0.187-0.185     C81.095,294.683,40.361,211.239,40.002,139.387c-0.186-37.276,11.027-68.389,32.431-90.014l0.153-0.138     c14.538-13.048,36.548-12.254,50.108,1.808c51.781,53.698,48.031,79.049,25.151,102.511l-37.074,37.353     c-5.814,5.858-7.433,14.686-4.075,22.226c0.941,2.114,23.709,52.427,80.414,109.132l2.632,2.629     c56.698,56.699,107.011,79.466,109.125,80.408c7.54,3.359,16.368,1.739,22.226-4.075l37.346-37.068     c23.466-22.883,48.818-26.638,102.518,25.145c14.062,13.56,14.856,35.57,1.81,50.105l-0.141,0.157     c-21.45,21.229-52.231,32.433-89.102,32.433c-0.303,0-0.608,0-0.912-0.002c-29.471-0.147-63.598-8.226-98.689-23.362     c-10.142-4.376-21.911,0.3-26.286,10.443c-4.375,10.142,0.301,21.911,10.443,26.286c40.562,17.496,79.029,26.456,114.332,26.633     c0.375,0.001,0.748,0.002,1.122,0.002c47.914-0.001,88.608-15.379,117.738-44.51c0.254-0.254,0.5-0.513,0.739-0.78l0.519-0.577     C505.766,451.385,512.663,432.357,511.949,412.553z"></path><path d="M369.457,142.549c-21.453-21.454-42.043-27.147-51.939-29.884c-10.649-2.945-21.663,3.299-24.607,13.946     c-2.944,10.646,3.299,21.663,13.946,24.607c8.092,2.238,20.32,5.62,34.316,19.615c13.473,13.473,17.052,25.636,19.421,33.685     l0.289,0.979c2.574,8.697,10.538,14.329,19.169,14.329c1.88,0.001,3.792-0.267,5.686-0.828     c10.591-3.135,16.636-14.263,13.5-24.854l-0.271-0.918C395.882,182.744,390.14,163.232,369.457,142.549z"></path>
</g>
</g>
</g></svg></span>
                    <h4 class="u-align-center-md u-align-center-sm u-align-center-xs u-text u-text-body-alt-color u-text-3">Call today</h4>
                    <a href="tel:+27658759087" class="u-btn u-button-style u-none u-text-active-palette-4-light-2 u-text-hover-palette-4-light-2 u-text-white u-btn-1"><b>************ / ************</b>
                    </a>
                  </div>
                </div>
                <div class="u-align-center-md u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-xl u-container-style u-layout-cell u-palette-3-base u-size-20 u-layout-cell-2">
                  <div class="u-container-layout u-valign-top u-container-layout-2">
                    <span class="u-icon u-icon-circle u-text-palette-3-light-2 u-icon-2"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 512 512" style=""><use xlink:href="#svg-b9b2"></use></svg><svg class="u-svg-content" viewBox="0 0 512 512" x="0px" y="0px" id="svg-b9b2" style="enable-background:new 0 0 512 512;"><g><g><path d="M511.738,494.272c-0.064-0.576,0.128-1.184,0-1.76l-64-287.968c-0.448-1.984-1.28-3.744-2.336-5.344    c-0.064-0.096-0.064-0.192-0.128-0.288c-0.256-0.352-0.64-0.544-0.928-0.896c-0.928-1.12-1.92-2.144-3.104-2.976    c-0.608-0.416-1.248-0.736-1.888-1.056c-1.056-0.544-2.112-0.992-3.296-1.28c-0.8-0.192-1.568-0.32-2.368-0.416    c-0.544-0.064-1.024-0.288-1.568-0.288h-192c-8.832,0-16,7.168-16,16c0,8.832,7.168,16,16,16H380.89L40.602,459.584    l37.376-168.128c1.92-8.64-3.52-17.184-12.16-19.104c-8.544-1.728-17.184,3.52-19.104,12.16l-46.208,208    c-0.032,0.096,0.032,0.192,0,0.288c-0.448,2.176-0.448,4.416,0.032,6.624c0.096,0.512,0.448,0.896,0.608,1.376    c0.48,1.472,0.928,2.912,1.824,4.256c0.096,0.16,0.288,0.192,0.416,0.352c0.128,0.16,0.128,0.384,0.256,0.544    c1.024,1.28,2.432,2.112,3.776,3.008c0.576,0.384,0.992,0.96,1.6,1.28c2.208,1.12,4.608,1.76,7.104,1.76h480    c2.688,0,5.216-0.8,7.52-2.048c0.704-0.384,1.184-0.96,1.824-1.44c1.056-0.8,2.272-1.408,3.136-2.496    c0.384-0.48,0.448-1.088,0.8-1.632c0.224-0.384,0.672-0.512,0.864-0.928c0.384-0.736,0.416-1.504,0.672-2.272    c0.32-0.896,0.64-1.696,0.768-2.624C511.962,497.088,511.898,495.712,511.738,494.272z M67.354,480l157.888-109.312L431.706,480    H67.354z M254.874,350.176L421.69,234.688l51.296,230.944L254.874,350.176z"></path>
</g>
</g><g><g><path d="M112.122,64c-26.464,0-48,21.536-48,48s21.536,48,48,48s48-21.536,48-48S138.586,64,112.122,64z M112.122,128    c-8.832,0-16-7.168-16-16c0-8.832,7.168-16,16-16c8.832,0,16,7.168,16,16C128.122,120.832,120.954,128,112.122,128z"></path>
</g>
</g><g><g><path d="M112.122,0c-61.76,0-112,50.24-112,112c0,57.472,89.856,159.264,100.096,170.688c3.04,3.36,7.36,5.312,11.904,5.312    s8.864-1.952,11.904-5.312C134.266,271.264,224.122,169.472,224.122,112C224.122,50.24,173.882,0,112.122,0z M112.122,247.584    c-34.944-41.44-80-105.056-80-135.584c0-44.096,35.904-80,80-80s80,35.904,80,80C192.122,142.496,147.066,206.144,112.122,247.584    z"></path>
</g>
</g></svg></span>
                    <h4 class="u-text u-text-body-alt-color u-text-4">Our location </h4>
                    <a href="#" class="u-btn u-button-style u-none u-text-active-palette-3-light-2 u-text-hover-palette-3-light-2 u-text-white u-btn-2">Cape Town and East London
                    </a>
                  </div>
                </div>
                <div class="u-align-center-md u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-xl u-container-style u-layout-cell u-palette-2-base u-size-20 u-layout-cell-3">
                  <div class="u-container-layout u-valign-top-lg u-valign-top-md u-valign-top-sm u-valign-top-xs u-container-layout-3">
                    <span class="u-icon u-icon-circle u-text-palette-2-light-2 u-icon-3"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 511.998 511.998" style=""><use xlink:href="#svg-76e4"></use></svg><svg class="u-svg-content" viewBox="0 0 511.998 511.998" id="svg-76e4"><g><path d="m431.665 139.258v-122.04h-351.332v122.04l-80.333 57.38v298.142h511.998v-298.142zm-249.673 202.811-151.992 108.564v-217.128zm74.007-15.994 194.191 138.705h-388.382zm74.007 15.994 151.992-108.564v217.127zm141.185-137.712-39.526 28.232v-56.464zm-69.526-157.139v206.8l-97.466 69.618-48.2-34.428-48.2 34.428-97.466-69.618v-206.8zm-321.332 185.372-39.526-28.232 39.526-28.232z"></path><path d="m152.716 86.074h103.283v30h-103.283z"></path><path d="m152.716 154.929h206.566v30h-206.566z"></path><path d="m152.716 223.785h206.566v30h-206.566z"></path>
</g></svg></span>
                    <h4 class="u-text u-text-body-alt-color u-text-5">call us</h4>
                    <a href="tel:+27658759087" class="u-btn u-button-style u-none u-text-active-palette-2-light-2 u-text-hover-palette-2-light-2 u-text-white u-btn-3"><b>************</b>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div class="u-size-30">
              <div class="u-layout-row">
                <div class="u-container-style u-image u-layout-cell u-size-60 u-image-1" data-image-width="1920" data-image-height="1280">
                  <div class="u-container-layout u-container-layout-4"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
   


    <!-- From Uiverse.io by somshri16 --> 
<section style="display: flex; justify-content: center; align-items: center; margin-top: 30px; margin-bottom: 30px;">
  <form method="post" action="https://public.herotofu.com/v1/ab9bb2b0-0b12-11f0-bd3e-b7254fc13091" class="form">
    <h2 class="u-align-center">Reach Out To Us</h2>
    <div>
        <label>
            <input name="first-name" required="" placeholder="" type="text" class="input">
            <span>first name</span>
        </label>
  
        
    </div>
    
    <div>
      <label>
        <input name="last-name" required="" placeholder="" type="text" class="input">
        <span>last name</span>
    </label>
    </div>
            
    <label>
        <input name="email" required="" placeholder="" type="email" class="input">
        <span>email</span>
    </label> 
        
    <label>
        <input name="contact-number" required="" type="tel" placeholder="" class="input">
        <span>contact number</span>
    </label>
    <label>
        <textarea name="message" required="" rows="3" placeholder="" class="input01"></textarea>
        <span>message</span>
    </label>
    
    <button class="fancy" href="#">
      <span class="top-key"></span>
      <span class="text">submit</span>
      <span class="bottom-key-1"></span>
      <span class="bottom-key-2"></span>
    </button>
  </form>
  
</section>
<style>
  /* From Uiverse.io by somshri16 */ 
.form {
  display: flex;
  flex-direction: column;
  gap: 10px;
 width: 600px;
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  position: relative;
}

.message {
  color: rgba(88, 87, 87, 0.822);
  font-size: 14px;
}

.flex {
  display: flex;
  width: 100%;
  gap: 6px;
}

.form label {
  position: relative;
}

.form label .input {
  width: 100%;
  padding: 10px 10px 20px 10px;
  outline: 0;
  border: 1px solid rgba(105, 105, 105, 0.397);
  border-radius: 5px;
}

.form label .input + span {
  position: absolute;
  left: 10px;
  top: 15px;
  color: grey;
  font-size: 0.9em;
  cursor: text;
  transition: 0.3s ease;
}

.form label .input:placeholder-shown + span {
  top: 15px;
  font-size: 0.9em;
}

.form label .input:focus + span,.form label .input:valid + span {
  top: 30px;
  font-size: 0.7em;
  font-weight: 600;
}

.form label .input:valid + span {
  color: green;
}

.input01 {
  width: 100%;
  padding: 10px 10px 20px 10px;
  outline: 0;
  border: 1px solid rgba(105, 105, 105, 0.397);
  border-radius: 5px;
}

.form label .input01 + span {
  position: absolute;
  left: 10px;
  top: 50px;
  color: grey;
  font-size: 0.9em;
  cursor: text;
  transition: 0.3s ease;
}

.form label .input01:placeholder-shown + span {
  top: 40px;
  font-size: 0.9em;
}

.form label .input01:focus + span,.form label .input01:valid + span {
  top: 50px;
  font-size: 0.7em;
  font-weight: 600;
}

.form label .input01:valid + span {
  color: green;
}

.fancy {
  background-color: transparent;
  border: 2px solid #cacaca;
  border-radius: 0px;
  box-sizing: border-box;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-weight: 390;
  letter-spacing: 2px;
  margin: 0;
  outline: none;
  overflow: visible;
  padding: 8px 30px;
  position: relative;
  text-align: center;
  text-decoration: none;
  text-transform: none;
  transition: all 0.3s ease-in-out;
  user-select: none;
  font-size: 13px;
}

.fancy::before {
  content: " ";
  width: 1.7rem;
  height: 2px;
  background: #cacaca;
  top: 50%;
  left: 1.5em;
  position: absolute;
  transform: translateY(-50%);
  transform: translateX(230%);
  transform-origin: center;
  transition: background 0.3s linear, width 0.3s linear;
}

.fancy .text {
  font-size: 1.125em;
  line-height: 1.33333em;
  padding-left: 2em;
  display: block;
  text-align: left;
  transition: all 0.3s ease-in-out;
  text-transform: lowercase;
  text-decoration: none;
  color: #818181;
  transform: translateX(30%);
}

.fancy .top-key {
  height: 2px;
  width: 1.5625rem;
  top: -2px;
  left: 0.625rem;
  position: absolute;
  background: white;
  transition: width 0.5s ease-out, left 0.3s ease-out;
}

.fancy .bottom-key-1 {
  height: 2px;
  width: 1.5625rem;
  right: 1.875rem;
  bottom: -2px;
  position: absolute;
  background: white;
  transition: width 0.5s ease-out, right 0.3s ease-out;
}

.fancy .bottom-key-2 {
  height: 2px;
  width: 0.625rem;
  right: 0.625rem;
  bottom: -2px;
  position: absolute;
  background: white;
  transition: width 0.5s ease-out, right 0.3s ease-out;
}

.fancy:hover {
  color: white;
  background: #cacaca;
}

.fancy:hover::before {
  width: 1.5rem;
  background: white;
}

.fancy:hover .text {
  color: white;
  padding-left: 1.5em;
}

.fancy:hover .top-key {
  left: -2px;
  width: 0px;
}

.fancy:hover .bottom-key-1,
 .fancy:hover .bottom-key-2 {
  right: 0;
  width: 0;
}
</style>
    
    
    
    <footer class="bg-gray-900 text-white">
      <div class="container mx-auto px-4 py-12">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                  <h3 class="text-xl font-bold mb-4">Jane & Peace Enterprises</h3>
                  <p class="mb-4">
                      We deliver end-to-end construction, renovation, and maintenance solutions for residential, commercial, and public sector clients.
                  </p>
                  <div class="flex space-x-4">
                      <a href="#" class="text-white hover:text-primary" aria-label="Facebook">
                          <i class="fab fa-facebook h-5 w-5"></i>
                      </a>
                      <a href="#" class="text-white hover:text-primary" aria-label="Instagram">
                          <i class="fab fa-instagram h-5 w-5"></i>
                      </a>
                      <a href="#" class="text-white hover:text-primary" aria-label="LinkedIn">
                          <i class="fab fa-linkedin h-5 w-5"></i>
                      </a>
                  </div>
              </div>
              
              <div>
                <h3 class="text-xl font-bold mb-4">Quick Links</h3>
                <ul class="space-y-2">
                    <li>
                        <a href="/" class="hover:text-primary">Home</a>
                    </li>
                    <li>
                        <a href="./Services.html" class="hover:text-primary">Services</a>
                    </li>
                    <li>
                        <a href="./About.html" class="hover:text-primary">About</a>
                    </li>
                    <li>
                        <a href="./Contact.html" class="hover:text-primary">Contact</a>
                    </li>
                </ul>
            </div>
              
              <div>
                  <h3 class="text-xl font-bold mb-4">Contact Us</h3>
                  <ul class="space-y-3">
                      <li class="flex items-start">
                          <i class="fas fa-map-marker-alt h-5 w-5 mr-2 mt-0.5"></i>
                          <span>Cape Town and East London</span>
                      </li>
                      <li class="flex items-center">
                          <i class="fas fa-phone h-5 w-5 mr-2"></i>
                          <span>************</span>
                      </li>
                      <li class="flex items-center">
                          <i class="fas fa-phone h-5 w-5 mr-2"></i>
                          <span>************</span>
                      </li>
                  </ul>
              </div>
          </div>
          
          <div class="border-t border-gray-800 mt-12 pt-8 text-center">
              <p>&copy; <span id="current-year"></span> Jane & Peace Enterprises. All rights reserved.</p>
          </div>
      </div>

      <div class="u-clearfix u-sheet u-sheet-1">
        <div class="u-align-center u-clearfix u-custom-html u-expanded-width u-custom-html-1">
          <div id="google_translate_element"></div>
          <script type="text/javascript"> function googleTranslateElementInit() {
  new google.translate.TranslateElement({pageLanguage: 'en'}, 'google_translate_element');
} </script>
          <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
        </div>
      </div>
  </footer>
  
</body></html>