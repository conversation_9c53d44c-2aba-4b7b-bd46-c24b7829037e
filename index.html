<!DOCTYPE html>
<html style="font-size: 16px;" lang="en"><head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="utf-8">
    <meta name="keywords" content="​Our Services, ​Building Your Future, Transforming Communities​, ​Building Dreams, Empowering Communities Since 2016, Our Services​​, Post 6 Headline, Post 5 Headline, Post 4 Headline, Post 3 Headline">
    <meta name="description" content="">
    <title>Home</title>
    <link rel="stylesheet" href="nicepage.css" media="screen">
<link rel="stylesheet" href="index.css" media="screen">
    <script class="u-script" type="text/javascript" src="jquery.js" defer=""></script>
    <script class="u-script" type="text/javascript" src="nicepage.js" defer=""></script>
    <meta name="generator" content="Nicepage 7.6.5, nicepage.com">

    
    
    
    <link id="u-theme-google-font" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:100,100i,300,300i,400,400i,500,500i,700,700i,900,900i|Open+Sans:300,300i,400,400i,500,500i,600,600i,700,700i,800,800i">
    <script type="application/ld+json">{
		"@context": "http://schema.org",
		"@type": "Organization",
		"name": "",
		"url": "/",
		"logo": "images/logo.png",
		"sameAs": [
				"https://facebook.com/name",
				"https://twitter.com/name",
				"https://www.instagram.com/name"
		]
}</script>


<script src="https://cdn.tailwindcss.com"></script>
    
<!-- Custom Tailwind Config -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    primary: '#1e5b98',
                    secondary: '#f59e0b',
                }
            }
        }
    }
</script>


    <meta name="theme-color" content="#478ac9">
    <link rel="canonical" href="/">
    <meta name="twitter:site" content="@">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Home">
    <meta name="twitter:description" content="">
    <meta property="og:title" content="Home">
    <meta property="og:type" content="website">
  <meta data-intl-tel-input-cdn-path="intlTelInput/"></head>
  <body data-home-page-title="Home" data-path-to-root="./" data-include-products="false" class="u-body u-xl-mode" data-lang="en">
    
    <header class=" u-clearfix u-header u-section-row-container" id="header" data-animation-name="" data-animation-duration="0" data-animation-delay="0" data-animation-direction=""><div class="u-section-rows">
      <div class="u-hidden-xs u-section-row" id="sec-71fa">
        <div class="u-clearfix u-sheet u-valign-middle-lg u-valign-middle-md u-valign-middle-sm u-sheet-1">
          <a href="#" class="u-image u-logo u-image-1" data-image-width="602" data-image-height="631">
            <img src="../images/logo.png" class="u-logo-image u-logo-image-1" data-image-width="80">
          </a>
          <div class="u-social-icons u-social-icons-1">
            <a class="u-social-url" title="facebook" target="_blank" href="https://www.facebook.com/share/1AN9a562UX/?mibextid=qi2Omg">
              <span class="u-icon u-social-facebook u-social-icon u-icon-1"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 112 112" style=""><use xlink:href="#svg-7e28"></use></svg><svg class="u-svg-content" viewBox="0 0 112 112" x="0" y="0" id="svg-7e28"><circle fill="currentColor" cx="56.1" cy="56.1" r="55"></circle><path fill="#FFFFFF" d="M73.5,31.6h-9.1c-1.4,0-3.6,0.8-3.6,3.9v8.5h12.6L72,58.3H60.8v40.8H43.9V58.3h-8V43.9h8v-9.2
    c0-6.7,3.1-17,17-17h12.5v13.9H73.5z"></path></svg></span>
            </a>
            <a class="u-social-url" title="twitter" target="_blank" href="https://www.tiktok.com/@janeandpeace?_t=ZM-8v9UA47zw7L&_r=1">
              <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="35" height="35" viewBox="0 0 50 50">
                  <path d="M41,4H9C6.243,4,4,6.243,4,9v32c0,2.757,2.243,5,5,5h32c2.757,0,5-2.243,5-5V9C46,6.243,43.757,4,41,4z M37.006,22.323 c-0.227,0.021-0.457,0.035-0.69,0.035c-2.623,0-4.928-1.349-6.269-3.388c0,5.349,0,11.435,0,11.537c0,4.709-3.818,8.527-8.527,8.527 s-8.527-3.818-8.527-8.527s3.818-8.527,8.527-8.527c0.178,0,0.352,0.016,0.527,0.027v4.202c-0.175-0.021-0.347-0.053-0.527-0.053 c-2.404,0-4.352,1.948-4.352,4.352s1.948,4.352,4.352,4.352s4.527-1.894,4.527-4.298c0-0.095,0.042-19.594,0.042-19.594h4.016 c0.378,3.591,3.277,6.425,6.901,6.685V22.323z"></path>
              </svg>
            </a>
            <a class="u-social-url" title="instagram" target="_blank" href="https://www.instagram.com/p/DH4H_NWIZzT/?igsh=c21tMjVtMXpmZWM=">
              <span class="u-icon u-social-icon u-social-instagram u-icon-3"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 112 112" style=""><use xlink:href="#svg-d54d"></use></svg><svg class="u-svg-content" viewBox="0 0 112 112" x="0" y="0" id="svg-d54d"><circle fill="currentColor" cx="56.1" cy="56.1" r="55"></circle><path fill="#FFFFFF" d="M55.9,38.2c-9.9,0-17.9,8-17.9,17.9C38,66,46,74,55.9,74c9.9,0,17.9-8,17.9-17.9C73.8,46.2,65.8,38.2,55.9,38.2
    z M55.9,66.4c-5.7,0-10.3-4.6-10.3-10.3c-0.1-5.7,4.6-10.3,10.3-10.3c5.7,0,10.3,4.6,10.3,10.3C66.2,61.8,61.6,66.4,55.9,66.4z"></path><path fill="#FFFFFF" d="M74.3,33.5c-2.3,0-4.2,1.9-4.2,4.2s1.9,4.2,4.2,4.2s4.2-1.9,4.2-4.2S76.6,33.5,74.3,33.5z"></path><path fill="#FFFFFF" d="M73.1,21.3H38.6c-9.7,0-17.5,7.9-17.5,17.5v34.5c0,9.7,7.9,17.6,17.5,17.6h34.5c9.7,0,17.5-7.9,17.5-17.5V38.8
    C90.6,29.1,82.7,21.3,73.1,21.3z M83,73.3c0,5.5-4.5,9.9-9.9,9.9H38.6c-5.5,0-9.9-4.5-9.9-9.9V38.8c0-5.5,4.5-9.9,9.9-9.9h34.5
    c5.5,0,9.9,4.5,9.9,9.9V73.3z"></path></svg></span>
            </a>
          </div>
        </div>
      </div>
      
        <div class="u-grey-5 u-section-row u-section-row-2" id="sec-13b1">
          <div class="u-clearfix u-sheet u-valign-middle-lg u-valign-middle-md u-valign-middle-sm u-valign-middle-xl u-sheet-2">
            <a href="#" class="u-hidden-md u-hidden-sm u-hidden-xl u-image u-logo u-image-2" data-image-width="602" data-image-height="631">
              <img src="images/logo.png" class="u-logo-image u-logo-image-2" data-image-width="80">
            </a>
            <nav class="u-menu u-menu-one-level u-offcanvas u-menu-1">
              <div class="menu-collapse" style="font-size: 1rem; letter-spacing: 0px; font-weight: 700; text-transform: uppercase;">
                <a class="u-button-style u-custom-active-border-color u-custom-border u-custom-border-color u-custom-borders u-custom-hover-border-color u-custom-left-right-menu-spacing u-custom-text-active-color u-custom-text-color u-custom-text-hover-color u-custom-top-bottom-menu-spacing u-hamburger-link u-nav-link" href="#">
                  <svg class="u-svg-link" viewBox="0 0 24 24"><use xlink:href="#menu-hamburger"></use></svg>
                  <svg class="u-svg-content" version="1.1" id="menu-hamburger" viewBox="0 0 16 16" x="0px" y="0px" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg"><g><rect y="1" width="16" height="2"></rect><rect y="7" width="16" height="2"></rect><rect y="13" width="16" height="2"></rect>
</g></svg>
                </a>
              </div>
              <div class="u-custom-menu u-nav-container">
                <ul class="u-nav u-unstyled u-nav-1"><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="./" style="padding: 10px 20px;">Home</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="About.html" style="padding: 10px 20px;">About</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="Services.html" style="padding: 10px 20px;">Services</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="Contact.html" style="padding: 10px 20px;">Contact</a>
</li></ul>
              </div>
              <div class="u-custom-menu u-nav-container-collapse">
                <div class="u-black u-container-style u-inner-container-layout u-opacity u-opacity-95 u-sidenav">
                  <div class="u-inner-container-layout u-sidenav-overflow">
                    <div class="u-menu-close"></div>
                    <ul class="u-align-center u-nav u-popupmenu-items u-unstyled u-nav-2"><li class="u-nav-item"><a class="u-button-style u-nav-link" href="./">Home</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link" href="About.html">About</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link" href="Services.html">Services</a>
</li><li class="u-nav-item"><a class="u-button-style u-nav-link" href="Contact.html">Contact</a>
</li></ul>
                  </div>
                </div>
                <div class="u-black u-menu-overlay u-opacity u-opacity-70"></div>
              </div>
            </nav>
          </div>
        </div>
        
      </div></header>
    <section style="background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('./images/umit-yildirim-9OB46apMbC4-unsplash.jpg'); background-position: center; background-size: cover; background-repeat: no-repeat;" class="skrollable skrollable-between u-align-center u-clearfix u-container-align-center u-image u-parallax u-shading u-section-1"  id="sec-8bef" src="" data-image-width="1920" data-image-height="1280">
      <div class="u-clearfix u-sheet u-valign-middle-lg u-valign-middle-md u-valign-middle-sm u-valign-middle-xs u-sheet-1">
        <h1 class="u-align-center u-text u-text-1" data-animation-name="customAnimationIn" data-animation-duration="1500"><b>Building Your Future, Transforming Communities </b>
        </h1>
        <p class="u-align-center u-large-text u-text u-text-variant u-text-2" data-animation-name="customAnimationIn" data-animation-duration="1500" data-animation-delay="250"><b></b>Jane &amp; Peace Enterprises is a construction and services company based in Cape Town and East London.
        </p>
        <a href="/Contact.html" class="u-active-white u-align-center u-border-none u-btn u-button-style u-hover-white u-palette-1-base u-text-active-palette-1-base u-text-hover-palette-1-base u-btn-1" data-animation-name="customAnimationIn" data-animation-duration="1500" data-animation-delay="200" data-animation-direction=""><b>Get a Free Quote</b>
        </a>
      </div>
      
    </section>

    <div style="position: fixed; bottom: 40px; right: 3px; z-index: 100000;">
      <a target="_blank" href="https://wa.me/qr/TY4XTAFKUWG2N1">
          <img  class="whatsapp-icon-1" style="" src="/images/whatsapp-chat.png"/>
      </a>
  </div>

  <style>
      .whatsapp-icon-1{
          width: 110px; height: auto;
      }

      @media only screen and (max-width: 600px) {
          .whatsapp-icon-1{
          width: 80px; height: auto;
      }
      
      }


  </style>
    <section class="u-clearfix u-container-align-center u-section-2" id="sec-d68f">
      <div class="u-clearfix u-sheet u-valign-middle u-sheet-1">
        <p class="u-align-center u-text u-text-grey-70 u-text-1" spellcheck="false"><b>We specialize in civil engineering, general building, plastering, tiling, deep cleaning, waste management, paving, pest control, waterproofing, plumbing, painting, electrical repairs and more for residential, commercial, and public sector clients. Contact us for quality construction and maintenance solutions.</b>
        </p>
      </div>
    </section>
    <section class="u-clearfix u-white u-section-3" id="sec-1cd7">
      <div class="u-clearfix u-sheet u-valign-middle-sm u-valign-top-xs u-sheet-1">
        <div class="u-expanded-height-lg u-expanded-height-md u-expanded-height-sm u-expanded-height-xl u-palette-3-base u-radius u-shape u-shape-round u-shape-1" data-animation-name="customAnimationIn" data-animation-duration="1000" data-animation-delay="0"></div>
        <img class="u-image u-image-round u-radius u-image-1" src="images/image3.png" data-animation-name="customAnimationIn" data-animation-duration="1000" data-animation-delay="0" data-image-width="713" data-image-height="580">
        <div class="u-align-center u-container-align-center u-container-style u-grey-5 u-group u-radius u-shape-round u-group-1" data-animation-name="customAnimationIn" data-animation-duration="1000" data-animation-delay="0">
          <div class="u-container-layout u-valign-middle-lg u-valign-middle-md u-valign-middle-sm u-valign-middle-xs u-container-layout-1">
            <h2 class="u-text u-text-default u-text-1" spellcheck="false"><b>Building Dreams, Empowering Communities Since 2016</b>
            </h2>
            <p class="u-text u-text-default u-text-grey-70 u-text-2" spellcheck="false"><b>Since our founding in 2016, we have built a reputation for quality workmanship, reliability, and community empowerment. We deliver end-to-end construction, renovation, and maintenance solutions for residential, commercial, and public sector clients. Our professional team is committed to turning your vision into reality—on time and within budget—while uplifting<span style="font-weight: 300;"></span> local communities along the way.</b>
            </p>
          </div>
        </div>
      </div>
    </section>
    <section class="u-align-center u-clearfix u-container-align-center u-white u-section-4" id="sec-708b">
      <div class="u-clearfix u-sheet u-valign-middle-lg u-valign-middle-md u-valign-middle-sm u-sheet-1">
        <h2 class="u-align-center u-text u-text-default u-text-1"> Our Services</h2>
        <p class="u-align-center u-text u-text-2">We offer a comprehensive range of construction and property services to meet all your needs </p>
        <div class="u-expanded-width u-list u-list-1">
          <div class="u-repeater u-repeater-1">
            
            <div class="u-container-style u-image u-image-round u-list-item u-radius u-repeater-item u-shading u-image-1" data-image-width="1280" data-image-height="853" data-animation-name="customAnimationIn" data-animation-duration="1500" data-animation-delay="500">
              <div class="u-container-layout u-similar-container u-valign-bottom u-container-layout-1">
                <h5 class="u-align-center u-text u-text-body-alt-color u-text-3"> Civil Engineering &amp; General Building</h5>
              </div>
            </div>

            <div class="u-container-style u-image u-image-round u-list-item u-radius u-repeater-item u-shading u-shape-rectangle u-image-2" data-image-width="1920" data-image-height="1450" data-animation-name="customAnimationIn" data-animation-duration="1500" data-animation-delay="500">
              <div class="u-container-layout u-similar-container u-valign-bottom u-container-layout-2">
                <h5 class="u-align-center u-text u-text-body-alt-color u-text-4"> Plastering, Tiling, Flooring &amp; Screeding</h5>
              </div>
            </div>
            <div class="u-container-style u-image u-image-round u-list-item u-radius u-repeater-item u-shading u-shape-rectangle u-image-3" data-image-width="1280" data-image-height="734" data-animation-name="customAnimationIn" data-animation-duration="1500" data-animation-delay="500">
              <div class="u-container-layout u-similar-container u-valign-bottom u-container-layout-3">
                <h5 class="u-align-center u-text u-text-body-alt-color u-text-5"> Deep Cleaning</h5>
              </div>
            </div>
            <div class="u-container-style u-image u-image-round u-list-item u-radius u-repeater-item u-shading u-shape-rectangle u-image-4" data-image-width="1280" data-image-height="854" data-animation-name="customAnimationIn" data-animation-duration="1500" data-animation-delay="500">
              <div class="u-container-layout u-similar-container u-valign-bottom u-container-layout-4">
                <h5 class="u-align-center u-text u-text-body-alt-color u-text-6"> Waste Management &amp; Rubble Removal</h5>
              </div>
            </div>
            <div class="u-container-style u-image u-image-round u-list-item u-radius u-repeater-item u-shading u-shape-rectangle u-image-5" data-image-width="1920" data-image-height="1280" data-animation-name="customAnimationIn" data-animation-duration="1500" data-animation-delay="500">
              <div class="u-container-layout u-similar-container u-valign-bottom u-container-layout-5">
                <h5 class="u-align-center u-text u-text-body-alt-color u-text-7"> Kerbs &amp; Paving</h5>
              </div>
            </div>
            <div class="u-container-style u-image u-image-round u-list-item u-radius u-repeater-item u-shading u-shape-rectangle u-image-6" data-image-width="1456" data-image-height="816" data-animation-name="customAnimationIn" data-animation-duration="1500" data-animation-delay="500">
              <div class="u-container-layout u-similar-container u-valign-bottom u-container-layout-6">
                <h5 class="u-align-center u-text u-text-body-alt-color u-text-8"> Pest Control</h5>
              </div>
            </div>
            <div class="u-container-style u-image u-image-round u-list-item u-radius u-repeater-item u-shading u-shape-rectangle u-image-7" data-image-width="1344" data-image-height="768" data-animation-name="customAnimationIn" data-animation-duration="1500" data-animation-delay="500">
              <div class="u-container-layout u-similar-container u-valign-bottom u-container-layout-7">
                <h5 class="u-align-center u-text u-text-body-alt-color u-text-9"> Waterproofing, Plumbing &amp; Painting</h5>
              </div>
            </div>
            <div class="u-container-style u-image u-image-round u-list-item u-radius u-repeater-item u-shading u-shape-rectangle u-image-8" data-image-width="1076" data-image-height="718" data-animation-name="customAnimationIn" data-animation-duration="1500" data-animation-delay="500">
              <div class="u-container-layout u-similar-container u-valign-bottom u-container-layout-8">
                <h5 class="u-align-center u-text u-text-body-alt-color u-text-10"> Electrical Repairs</h5>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <script>
      document.addEventListener("DOMContentLoaded", function() {
        // Select all items you want to be clickable
        const blogItems = document.querySelectorAll('.u-repeater-item');
    
        // Define a URL for each blog item (order should match the HTML structure)
        const pageUrls = [
          "/services/civil-engineering.html",
          "/services/plastering-tiling.html",
          "/services/deep-cleaning.html",
          "/services/waste-management.html",
          "/services/kerbs-paving.html",
          "/services/pest-control.html",
          "/services/maintenance-services.html",
          "/services/electrical-repairs.html"
        ];
    
        // Attach click event listener to each item
        blogItems.forEach((item, index) => {
          item.addEventListener("click", function() {
            // Check if a URL is defined for this item
            if (pageUrls[index]) {
              window.location.href = pageUrls[index];
            }
          });
        });
      });
    </script>


    <section class="u-clearfix u-container-align-left-lg u-container-align-left-xl u-section-5" id="sec-1e3f">
      <div class="u-clearfix u-sheet u-valign-middle-lg u-valign-middle-sm u-sheet-1">
        <div class="data-layout-selected u-clearfix u-expanded-width-lg u-expanded-width-xl u-expanded-width-xs u-layout-wrap u-layout-wrap-1">
          <div class="u-layout">
            <div class="u-layout-row">
              <div class="u-container-style u-layout-cell u-size-34 u-layout-cell-1">
                <div class="u-container-layout u-valign-bottom-lg u-container-layout-1">
                  <h2 class="u-align-center-xs u-align-left-lg u-align-left-md u-align-left-sm u-align-left-xl u-text u-text-1" spellcheck="false"> Why Choose Jane &amp; Peace Enterprises?</h2>
                  <div class="custom-expanded u-expanded-width-lg u-expanded-width-md u-expanded-width-sm u-expanded-width-xl u-list u-list-1">
                    <div class="u-repeater u-repeater-1">
                      <div class="u-container-style u-grey-5 u-list-item u-radius u-repeater-item u-shape-round u-list-item-1">
                        <div class="u-container-layout u-similar-container u-valign-middle-xs u-container-layout-2">
                          <span class="u-align-left u-file-icon u-icon u-text-palette-4-base u-icon-1"><img src="images/87932-94027438.png" alt=""></span>
                          <h5 class="u-text u-text-default-xl u-text-2"> Experienced &amp; Versatile Team</h5>
                          <p class="u-text u-text-3" spellcheck="false"> Our skilled engineers, builders, cleaners, and technicians bring years of experience across multiple trades, ensuring top-quality results every time.</p>
                        </div>
                      </div>
                      <div class="u-container-style u-grey-5 u-list-item u-radius u-repeater-item u-shape-round u-list-item-2">
                        <div class="u-container-layout u-similar-container u-valign-middle-xs u-container-layout-3">
                          <span class="u-align-left u-file-icon u-icon u-text-palette-4-base u-icon-2"><img src="images/87932-94027438.png" alt=""></span>
                          <h5 class="u-text u-text-default-xl u-text-4"> Quality &amp; Safety Guaranteed</h5>
                          <p class="u-text u-text-5" spellcheck="false"> We uphold strict quality standards and adhere to all health and safety regulations. Every project is supervised and inspected for excellence and compliance, with an aim of zero incidents.</p>
                        </div>
                      </div>
                      <div class="u-container-style u-grey-5 u-list-item u-radius u-repeater-item u-shape-round u-list-item-3">
                        <div class="u-container-layout u-similar-container u-valign-middle-xs u-container-layout-4">
                          <span class="u-align-left u-file-icon u-icon u-text-palette-4-base u-icon-3"><img src="images/87932-94027438.png" alt=""></span>
                          <h5 class="u-text u-text-default-xl u-text-6"> One-Stop Solution</h5>
                          <p class="u-text u-text-7" spellcheck="false"> From building construction to post-project cleaning and maintenance, we offer all the services you need under one roof. This integrated approach saves you time and cost, and ensures consistent quality.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="u-container-style u-image u-image-round u-layout-cell u-radius u-size-26 u-image-1" data-image-width="1920" data-image-height="1280">
                <div class="u-container-layout u-container-layout-5"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="u-clearfix u-white u-section-6" id="sec-08dc">
      <div class="u-clearfix u-sheet u-sheet-1">
        <h2 class="u-text u-text-default u-text-1" spellcheck="false">Testimonials </h2>
        <div class="u-expanded-width u-list u-list-1">
          <div class="u-repeater u-repeater-1">
            <div class="u-container-align-center-sm u-container-align-center-xs u-container-style u-list-item u-radius u-repeater-item u-shape-round u-white u-list-item-1" data-animation-name="customAnimationIn" data-animation-duration="2000" data-animation-delay="0">
              <div class="u-container-layout u-similar-container u-valign-top u-container-layout-1">
                <h3 class="u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-md u-align-left-xl u-text u-text-2" spellcheck="false"> CapeTech Offices</h3>
                <p class="u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-md u-align-left-xl u-text u-text-default u-text-3" spellcheck="false"> Jane &amp; Peace Enterprises exceeded our expectations during our office park renovation. Their attention to detail and professionalism were outstanding</p>
                <h6 class="u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-md u-align-left-xl u-text u-text-4"> Michael D</h6>
              </div>
            </div>
            <div class="u-container-align-center-sm u-container-align-center-xs u-container-style u-list-item u-radius u-repeater-item u-shape-round u-white u-list-item-2" data-animation-name="customAnimationIn" data-animation-duration="2000" data-animation-delay="0">
              <div class="u-container-layout u-similar-container u-valign-top u-container-layout-2">
                <h3 class="u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-md u-align-left-xl u-text u-text-5" spellcheck="false"> Somerset West</h3>
                <p class="u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-md u-align-left-xl u-text u-text-default u-text-6" spellcheck="false"> Fantastic service! The team built our dream family home with quality workmanship and kept us informed every step of the way. We couldn’t be happier.</p>
                <h6 class="u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-md u-align-left-xl u-text u-text-7"> Sandra P</h6>
              </div>
            </div>
            <div class="u-container-align-center-sm u-container-align-center-xs u-container-style u-list-item u-radius u-repeater-item u-shape-round u-white u-list-item-3" data-animation-name="customAnimationIn" data-animation-duration="2000" data-animation-delay="0">
              <div class="u-container-layout u-similar-container u-valign-top u-container-layout-3">
                <h3 class="u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-md u-align-left-xl u-text u-text-8" spellcheck="false"> Ikhaya Apartments</h3>
                <p class="u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-md u-align-left-xl u-text u-text-default u-text-9" spellcheck="false"> Reliable and efficient – their deep cleaning crew left our complex sparkling clean. We highly recommend Jane &amp; Peace for any construction or cleaning project!</p>
                <h6 class="u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-md u-align-left-xl u-text u-text-10"> Thabo M</h6>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <section>

      <style>
        :root {
            --primary: #478ac9;
            --secondary: #1d1d1d;
            --light: #f5f5f5;
            --gray: #777777;
            --dark: #222222;
            --transition: all 0.3s ease;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .references-header {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .references-header::after {
            content: '';
            display: block;
            width: 150px;
            height: 4px;
            background-color: var(--primary);
            margin: 15px auto;
        }

        .references-header h2 {
            font-size: 2.5rem;
            color: var(--dark);
            margin-bottom: 10px;
        }

        .references-header p {
            font-size: 1.1rem;
            color: var(--gray);
        }

        .category-filter {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
            gap: 10px;
        }

        .filter-btn {
            padding: 10px 20px;
            background-color: white;
            color: var(--dark);
            border: 2px solid var(--primary);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            border-radius: 5px;
        }

        .filter-btn:hover, .filter-btn.active {
            background-color: var(--primary);
            color: white;
        }

        .references-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            transition: var(--transition);
            width: 100%;
        }

        .reference-card {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            display: flex;
            flex-direction: column;
            opacity: 1;
            transform: scale(1);
            height: auto;
            min-height: 280px;
        }

        .reference-card.hidden {
            display: none;
        }

        .reference-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background-color: var(--primary);
            color: white;
            padding: 12px 15px;
            font-weight: bold;
            font-size: 1.1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header .category-tag {
            background-color: rgba(255, 255, 255, 0.3);
            padding: 3px 8px;
            border-radius: 20px;
            font-size: 0.65rem;
            text-transform: uppercase;
            white-space: nowrap;
        }

        .card-body {
            padding: 15px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .company-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            margin-bottom: 0;
            border: 2px solid white;
            position: relative;
            overflow: hidden;
            box-shadow: 0 3px 6px rgba(0,0,0,0.16);
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
            flex-shrink: 0;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        @media (max-width: 768px) {
            .logo-container {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .company-logo {
                margin-bottom: 5px;
            }
        }

        .card-body h4 {
            margin-bottom: 10px;
            color: var(--primary);
        }

        .contact-info {
            margin-top: 15px;
        }

        .contact-info div {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            font-size: 0.95rem;
            line-height: 1.4;
        }

        .contact-info i {
            margin-right: 10px;
            color: var(--primary);
        }

        .contact-btn {
            display: block;
            width: 100%;
            margin-top: 15px;
            padding: 10px 15px;
            background-color: var(--primary);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 600;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            text-align: center;
            font-size: 0.95rem;
        }

        .contact-btn:hover {
            background-color: var(--dark);
        }

        @media (max-width: 768px) {
            .references-grid {
                grid-template-columns: 1fr;
                padding: 0 10px;
            }
            
            .reference-card {
                height: auto;
                min-height: 250px;
                width: 100%;
                max-width: 400px;
                margin: 0 auto;
            }
            
            .references-header h2 {
                font-size: 2rem;
            }
            
            .logo-container {
                flex-direction: row;
                align-items: center;
                gap: 10px;
            }
            
            .company-logo {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
                flex-shrink: 0;
            }
            
            .card-body h4 {
                font-size: 1rem;
                word-break: break-word;
            }
            
            .category-filter {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .filter-btn {
                padding: 8px 12px;
                font-size: 0.9rem;
                margin-bottom: 5px;
            }
        }
        .icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 8px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .icon-company {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23f37021' viewBox='0 0 16 16'%3E%3Cpath d='M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zm3.669 11.538a.498.498 0 0 0-.686-.165l-1.73-2.399a.896.896 0 0 0-.725 0l-1.73 2.399a.498.498 0 0 0-.686.165.503.503 0 0 0 .165.687l2.05-2.688a.496.496 0 0 0 .7 0l2.05 2.688a.501.501 0 0 0 .685-.165c.184-.232.147-.568-.083-.753zm.979-3.312a.997.997 0 0 0-.282-.74l-1.29-1.293-1.29 1.292a.997.997 0 0 0-.282.74v.139c0 .552.448 1 1 1h1.143c.552 0 1-.45 1-1zm-2.572-2.006l1.292-1.292a.999.999 0 0 0 0-1.414 1 1 0 0 0-1.415 0l-1.292 1.292 1.415 1.414zm-2.172-1.2L6.29 3.414a.999.999 0 0 0 0-1.414 1 1 0 0 0-1.415 0L3.29 3.294l1.415 1.414z'/%3E%3C/svg%3E");
        }

        .icon-person {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23f37021' viewBox='0 0 16 16'%3E%3Cpath d='M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10z'/%3E%3C/svg%3E");
        }

        .icon-phone {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23f37021' viewBox='0 0 16 16'%3E%3Cpath d='M11 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h6zM5 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H5z'/%3E%3Cpath d='M8 14a1 1 0 1 0 0-2 1 1 0 0 0 0 2z'/%3E%3C/svg%3E");
        }
    </style>
  
  <section class="references-section">
    <div class="container">
        <div class="references-header">
            <h2>Our References</h2>
            <p>We are proud to have worked with these reputable companies and contractors</p>
        </div>

        <div class="category-filter">
            <button class="filter-btn active" data-filter="all">All Services</button>
            <button class="filter-btn" data-filter="tiling">Tiling</button>
            <button class="filter-btn" data-filter="cleaning">Cleaning</button>
            <button class="filter-btn" data-filter="waste">Waste Management</button>
            <button class="filter-btn" data-filter="paving">Kerbs & Paving</button>
            <button class="filter-btn" data-filter="electrical">Electrical</button>
            <button class="filter-btn" data-filter="painting">Painting & Plastering</button>
        </div>

        <div class="references-grid" id="referencesGrid">
            <!-- References will be added here via JavaScript -->
        </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Reference data from the company profile
        const referencesData = [
            {
                category: "painting",
                company: "BAY SIDE MALL",
                contactPerson: "Rubben Swart",
                contactNumber: "064 182 6566",
                service: "Painting and Plastering",
            },
            {
                category: "tiling",
                company: "PENINSULA CONSTRUCTION (PYT) LTD",
                contactPerson: "THEMBA",
                contactNumber: "076 705 2893",
                service: "Tiling",
            },
            {
                category: "painting",
                company: "STABILID CAPE CONSTRUCTION",
                contactPerson: "Rubben Swart",
                contactNumber: "064 182 6566",
                service: "Painting and Plastering",
            },
            {
                category: "tiling",
                company: "PREXUS CONSTRUCTION",
                contactPerson: "Nicholas",
                contactNumber: "083 506 5421",
                service: "Tiling",
            },
            {
                category: "cleaning",
                company: "ASK OSCAR",
                contactPerson: "Clint Crowster",
                contactNumber: "021 705 0653",
                service: "Cleaning",
            },
            {
                category: "waste",
                company: "STABILID BAY SIDE MALL",
                contactPerson: "Ruben Swart",
                contactNumber: "064 182 6566",
                service: "Waste Management Removal",
            },
            {
                category: "waste",
                company: "PRAXIS",
                contactPerson: "Simphiwe Thethani",
                contactNumber: "081 318 8611",
                service: "Waste Management Removal",
            },
            {
                category: "waste",
                company: "TERRA FIRMA",
                contactPerson: "Parusha Malepfane",
                contactNumber: "081 463 2038 / 011 568 7101",
                service: "Waste Management / Rubble Removal",
            },
            {
                category: "paving",
                company: "EXEO",
                contactPerson: "Marquin",
                contactNumber: "082 078 9684",
                service: "Kerbs and Paving",
            },
            {
                category: "electrical",
                company: "OWNHAVEN (CAPE TOWN BRANCH)",
                contactPerson: "Fradreck Nyahwai (Facilities Officer)",
                contactNumber: "069 442 7303 / ************",
                service: "Deep Cleaning / Pest Control / Electrical Repairs",
            },
            {
                category: "cleaning",
                company: "OWNHAVEN (EAST LONDON BRANCH)",
                contactPerson: "Mzubanzi Mbi (Facilities Officer)",
                contactNumber: "************ / ************",
                service: "Deep Cleaning",
            }
        ];

        // Function to generate a unique background pattern for logos
        function generateLogoBackground(seed) {
            // Create a simple hash from the company name to get consistent colors
            let hash = 0;
            for (let i = 0; i < seed.length; i++) {
                hash = seed.charCodeAt(i) + ((hash << 5) - hash);
            }
            
            // Generate a base hue from the hash (0-360)
            const hue = hash % 360;
            
            // Assign a color with the company's unique hue
            return {
                background: `linear-gradient(135deg, hsl(${hue}, 70%, 60%), hsl(${hue}, 80%, 40%))`,
                color: '#ffffff'
            };
        }

        // Generate reference cards
        const referencesGrid = document.getElementById('referencesGrid');
        
        referencesData.forEach(reference => {
            const card = document.createElement('div');
            card.classList.add('reference-card');
            card.setAttribute('data-category', reference.category);
            
            // Generate initials for logo
            const initials = reference.company.split(' ')
                .map(word => word[0])
                .join('')
                .substring(0, 2)
                .toUpperCase();
            
            // Generate unique logo background
            const logoStyle = generateLogoBackground(reference.company);
            
            card.innerHTML = `
                <div class="card-header">
                    <span>Reference</span>
                    <span class="category-tag">${reference.service}</span>
                </div>
                <div class="card-body">
                    <div>
                        <div class="logo-container">
                            <div class="company-logo" style="background: ${logoStyle.background}; color: ${logoStyle.color};">${initials}</div>
                            <h4>${reference.company}</h4>
                        </div>
                        <div class="contact-info">
                            <div><span class="icon icon-person"></span>${reference.contactPerson}</div>
                            <div><span class="icon icon-phone"></span>${reference.contactNumber.replace('/', ' / ')}</div>
                        </div>
                    </div>
                    <button class="contact-btn">Contact Reference</button>
                </div>
            `;
            
            referencesGrid.appendChild(card);
        });

        // Filter functionality
        const filterButtons = document.querySelectorAll('.filter-btn');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));
                
                // Add active class to clicked button
                this.classList.add('active');
                
                const filterValue = this.getAttribute('data-filter');
                
                // Filter cards
                const cards = document.querySelectorAll('.reference-card');
                
                cards.forEach(card => {
                    if (filterValue === 'all' || card.getAttribute('data-category') === filterValue) {
                        card.classList.remove('hidden');
                    } else {
                        card.classList.add('hidden');
                    }
                });
            });
        });

        // Handle contact button clicks
        document.querySelectorAll('.contact-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const card = this.closest('.reference-card');
                const company = card.querySelector('h4').textContent;
                const contactPerson = card.querySelectorAll('.contact-info div')[0].textContent;
                const contactNumber = card.querySelectorAll('.contact-info div')[1].textContent;
                
                alert(`Contact Reference\nCompany: ${company}\nContact: ${contactPerson}\nPhone: ${contactNumber}`);
                
                // In a real implementation, this could open a contact form or copy details to clipboard
            });
        });
    });
</script>


    </section>


    <section class="u-clearfix u-container-align-center u-section-7" id="sec-6aa7">
      <div class="u-clearfix u-sheet u-valign-middle-lg u-valign-middle-md u-valign-middle-sm u-valign-middle-xs u-sheet-1">
        <div class="data-layout-selected u-clearfix u-expanded-width u-layout-wrap u-layout-wrap-1">
          <div class="u-layout">
            <div class="u-layout-row">
              <div class="u-container-style u-layout-cell u-radius u-shape-round u-size-31-lg u-size-31-xl u-size-31-xxl u-size-60-md u-size-60-sm u-size-60-xs u-layout-cell-1">
                <div class="u-container-layout u-valign-middle-lg u-container-layout-1">
                  <img class="custom-expanded u-align-center-md u-align-center-sm u-align-center-xs u-image u-image-round u-radius u-image-1" src="images/construction_site.jpg" alt="" data-image-width="1920" data-image-height="1080" data-animation-name="flipIn" data-animation-duration="1500" data-animation-delay="0" data-animation-direction="X">
                </div>
              </div>
              <div class="u-container-align-left-lg u-container-align-left-xl u-container-style u-layout-cell u-shape-rectangle u-size-29-lg u-size-29-xl u-size-29-xxl u-size-60-md u-size-60-sm u-size-60-xs u-layout-cell-2" data-animation-name="customAnimationIn" data-animation-duration="1500" data-animation-delay="500">
                <div class="u-container-layout u-valign-middle u-container-layout-2">
                  <h2 class="u-align-center-md u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-xl u-text u-text-default u-text-1" spellcheck="false"> Ready to Start Your Project?</h2>
                  <p class="u-align-center-md u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-xl u-text u-text-default u-text-2" spellcheck="false"> Contact Jane &amp; Peace Enterprises today to discuss your next project or request a free, no-obligation quote. Our friendly team is here to answer your questions and assist you at every step.&nbsp;</p>
                  <a href="/Services.html" class="u-active-black u-align-center-md u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-xl u-btn u-button-style u-hover-black u-btn-1">Learn more&nbsp;&nbsp;<span class="u-file-icon u-icon u-text-white u-icon-1"><img src="images/152352-c9848c63.png" alt=""></span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <style data-mode="XXL" data-visited="true">@media (max-width: 0px) {
  .u-section-7 .u-sheet-1 {
    min-height: 884px;
  }
  .u-section-7 .u-layout-wrap-1 {
    margin-top: 60px;
    margin-bottom: 60px;
  }
  .u-section-7 .u-layout-cell-1 {
    min-height: 754px;
    --top-left-radius: 20px;
  }
  .u-section-7 .u-container-layout-1 {
    padding-top: 30px;
    padding-right: 30px;
    padding-bottom: 30px;
    padding-left: 0;
  }
  .u-section-7 .u-image-1 {
    width: 611px;
    height: 585px;
    object-position: 55.03% 0.15%;
    --radius: 10px;
    margin-top: 0;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: 0;
  }
  .u-block-3742-16 {
    min-height: 229px;
    width: 370px;
    height: auto;
    --radius: 10px;
    margin-top: -140px;
    margin-right: 0;
    margin-bottom: 0;
    margin-left: auto;
  }
  .u-block-3742-17 {
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 20px;
    padding-right: 20px;
  }
  .u-block-3742-18 {
    margin-top: 0;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: 0;
  }
  .u-section-7 .u-layout-cell-2 {
    min-height: 754px;
    --animation-custom_in-translate_x: 300px;
    --animation-custom_in-translate_y: 0px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
  }
  .u-section-7 .u-container-layout-2 {
    padding-top: 30px;
    padding-right: 0;
    padding-bottom: 30px;
    padding-left: 40px;
  }
  .u-block-3742-6 {
    margin-top: 0;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: 0;
  }
  .u-section-7 .u-text-1 {
    margin-top: 20px;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: 0;
    font-size: 3.7647117647058828rem;
  }
  .u-section-7 .u-text-2 {
    margin-top: 17px;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: 0;
  }
  .u-block-3742-12 {
    margin-top: 30px;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 0;
  }
  .u-block-3742-13 {
    background-image: none;
    font-weight: 400;
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
  }
  .u-section-7 .u-btn-1 {
    margin-top: 40px;
    margin-right: auto;
    margin-bottom: 0;
    margin-left: 0;
    padding-top: 19px;
    padding-right: 49px;
    padding-bottom: 19px;
    padding-left: 47px;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 1rem;
    letter-spacing: 1px;
  }
  .u-section-7 .u-icon-1 {
    font-size: 1rem;
    margin-left: 0;
    vertical-align: -1px;
  }
}</style>
    </section>
    <section class="u-align-left u-clearfix u-container-align-left u-container-align-left-lg u-container-align-left-md u-container-align-left-sm u-container-align-left-xl u-section-8" id="sec-59e0" style="margin-bottom: 50px;">
      <div class="u-clearfix u-sheet u-sheet-1">
        <h2 class="u-align-center-xs u-text u-text-1">Faq</h2>
        <div class="u-accordion u-expanded-width u-faq u-spacing-20 u-accordion-1">
          <div class="u-accordion-item">
            <a class="active u-accordion-link u-active-grey-5 u-border-2 u-border-active-palette-1-base u-border-hover-palette-5-dark-2 u-border-no-left u-border-no-right u-border-no-top u-border-palette-5-dark-2 u-button-style u-text-body-color u-accordion-link-1" id="link-accordion-6327" aria-controls="accordion-6327" aria-selected="true">
              <span class="u-accordion-link-text"> What services does Jane &amp; Peace Enterprises provide?</span>
              <span class="u-accordion-link-icon u-icon u-icon-circle u-text-black u-icon-1"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 16 16" style=""><use xlink:href="#svg-2c6d"></use></svg><svg class="u-svg-content" viewBox="0 0 16 16" x="0px" y="0px" id="svg-2c6d" style=""><path d="M8,10.7L1.6,5.3c-0.4-0.4-1-0.4-1.3,0c-0.4,0.4-0.4,0.9,0,1.3l7.2,6.1c0.1,0.1,0.4,0.2,0.6,0.2s0.4-0.1,0.6-0.2l7.1-6
	c0.4-0.4,0.4-0.9,0-1.3c-0.4-0.4-1-0.4-1.3,0L8,10.7z"></path></svg></span>
            </a>
            <div class="u-accordion-active u-accordion-pane u-container-style u-accordion-pane-1" id="accordion-6327" aria-labelledby="link-accordion-6327" aria-expanded="true">
              <div class="u-container-layout u-container-layout-1">
                <div class="fr-view u-clearfix u-rich-text u-text">
                  <p>We are a multi-service construction and maintenance company. Our offerings range from civil engineering and general building construction to finishing trades like plastering, tiling, flooring, and painting. We also provide deep cleaning, waste management and rubble removal, paving and kerb installation, pest control, waterproofing and plumbing repairs, as well as electrical maintenance. Essentially, we can handle everything from building you a new structure to keeping your property well-maintained afterward.</p>
                </div>
              </div>
            </div>
          </div>
          <div class="u-accordion-item">
            <a class="u-accordion-link u-active-grey-5 u-border-2 u-border-active-palette-1-base u-border-hover-palette-5-dark-2 u-border-no-left u-border-no-right u-border-no-top u-border-palette-5-dark-2 u-button-style u-text-body-color u-accordion-link-2" id="link-accordion-4aab" aria-controls="accordion-4aab" aria-selected="false">
              <span class="u-accordion-link-text"><b>Which areas do you serve?</b>
              </span>
              <span class="u-accordion-link-icon u-icon u-icon-circle u-text-black u-icon-2"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 16 16" style=""><use xlink:href="#svg-937a"></use></svg><svg class="u-svg-content" viewBox="0 0 16 16" x="0px" y="0px" id="svg-937a" style=""><path d="M8,10.7L1.6,5.3c-0.4-0.4-1-0.4-1.3,0c-0.4,0.4-0.4,0.9,0,1.3l7.2,6.1c0.1,0.1,0.4,0.2,0.6,0.2s0.4-0.1,0.6-0.2l7.1-6
	c0.4-0.4,0.4-0.9,0-1.3c-0.4-0.4-1-0.4-1.3,0L8,10.7z"></path></svg></span>
            </a>
            <div class="u-accordion-pane u-container-style u-accordion-pane-2" id="accordion-4aab" aria-labelledby="link-accordion-4aab" aria-expanded="true">
              <div class="u-container-layout u-container-layout-2">
                <div class="fr-view u-clearfix u-rich-text u-text">
                  <p dir="ltr" id="isPasted">We are based in Cape Town and primarily serve the greater Cape Town metropolitan area and surrounding regions in the Western Cape. We have completed projects throughout Cape Town’s suburbs and townships, and we are open to projects across the Western Cape province. For larger contracts (especially in the public sector), we can operate in other provinces as needed, but our focus is on our local community in and around Cape Town.</p>
                </div>
              </div>
            </div>
          </div>
          <div class="u-accordion-item">
            <a class="u-accordion-link u-active-grey-5 u-border-2 u-border-active-palette-1-base u-border-hover-palette-5-dark-2 u-border-no-left u-border-no-right u-border-no-top u-border-palette-5-dark-2 u-button-style u-text-body-color u-accordion-link-3" id="link-accordion-eb1f" aria-controls="accordion-eb1f" aria-selected="false">
              <span class="u-accordion-link-text"><b>Is Jane &amp; Peace Enterprises a registered and insured contractor?</b>
              </span>
              <span class="u-accordion-link-icon u-icon u-icon-circle u-text-black u-icon-3"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 16 16" style=""><use xlink:href="#svg-4086"></use></svg><svg class="u-svg-content" viewBox="0 0 16 16" x="0px" y="0px" id="svg-4086" style=""><path d="M8,10.7L1.6,5.3c-0.4-0.4-1-0.4-1.3,0c-0.4,0.4-0.4,0.9,0,1.3l7.2,6.1c0.1,0.1,0.4,0.2,0.6,0.2s0.4-0.1,0.6-0.2l7.1-6
	c0.4-0.4,0.4-0.9,0-1.3c-0.4-0.4-1-0.4-1.3,0L8,10.7z"></path></svg></span>
            </a>
            <div class="u-accordion-pane u-container-style u-accordion-pane-3" id="accordion-eb1f" aria-labelledby="link-accordion-eb1f" aria-expanded="true">
              <div class="u-container-layout u-container-layout-3">
                <div class="fr-view u-clearfix u-rich-text u-text">
                  <p>Yes. We are a fully registered company in South Africa (Jane &amp; Peace Enterprises Pty Ltd) and comply with all local building and trade regulations. We are also a Level 1 B-BBEE contributor, which underlines our formal compliance and commitment to empowerment. In terms of insurance, we carry comprehensive public liability insurance and Workman’s Compensation coverage to protect our clients, employees, and any third parties on our sites. We prioritize safety and professionalism, giving you peace of mind when you work with us.</p>
                </div>
              </div>
            </div>
          </div>
          <div class="u-accordion-item u-expanded-width">
            <a class="u-accordion-link u-active-grey-5 u-border-2 u-border-active-palette-1-base u-border-hover-palette-5-dark-2 u-border-no-left u-border-no-right u-border-no-top u-border-palette-5-dark-2 u-button-style u-text-body-color u-accordion-link-4" id="link-accordion-eb1f" aria-controls="accordion-eb1f" aria-selected="false">
              <span class="u-accordion-link-text"><b>How do I request a quote for my project?</b>
              </span>
              <span class="u-accordion-link-icon u-icon u-icon-circle u-text-black u-icon-4"><svg class="u-svg-link" preserveAspectRatio="xMidYMin slice" viewBox="0 0 16 16" style=""><use xlink:href="#svg-df61"></use></svg><svg class="u-svg-content" viewBox="0 0 16 16" x="0px" y="0px" id="svg-df61" style=""><path d="M8,10.7L1.6,5.3c-0.4-0.4-1-0.4-1.3,0c-0.4,0.4-0.4,0.9,0,1.3l7.2,6.1c0.1,0.1,0.4,0.2,0.6,0.2s0.4-0.1,0.6-0.2l7.1-6
	c0.4-0.4,0.4-0.9,0-1.3c-0.4-0.4-1-0.4-1.3,0L8,10.7z"></path></svg></span>
            </a>
            <div class="u-accordion-pane u-container-style u-accordion-pane-4" id="accordion-eb1f" aria-labelledby="link-accordion-eb1f" aria-expanded="true">
              <div class="u-container-layout u-container-layout-4">
                <div class="fr-view u-clearfix u-rich-text u-text">
                  <p>It’s simple – you can call us directly to discuss your needs or fill out our online quote request form on the Contact Us page. Provide as much detail as possible about your project (location, scope, timeline, etc.), and we will arrange a consultation or site visit if necessary. After evaluating your requirements, we’ll provide you with a detailed, transparent quotation at no cost. Our quotes are free and come with no obligation. We aim to give you a fair and competitive price, and we’re happy to discuss options to fit your budget.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    
    
    <footer class="bg-gray-900 text-white">
      <div class="container mx-auto px-4 py-12">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                  <h3 class="text-xl font-bold mb-4">Jane & Peace Enterprises</h3>
                  <p class="mb-4">
                      We deliver end-to-end construction, renovation, and maintenance solutions for residential, commercial, and public sector clients.
                  </p>
                  <div class="flex space-x-4">
                      <a href="#" class="text-white hover:text-primary" aria-label="Facebook">
                          <i class="fab fa-facebook h-5 w-5"></i>
                      </a>
                      <a href="#" class="text-white hover:text-primary" aria-label="Instagram">
                          <i class="fab fa-instagram h-5 w-5"></i>
                      </a>
                      <a href="#" class="text-white hover:text-primary" aria-label="LinkedIn">
                          <i class="fab fa-linkedin h-5 w-5"></i>
                      </a>
                  </div>
              </div>
              
              <div>
                  <h3 class="text-xl font-bold mb-4">Quick Links</h3>
                  <ul class="space-y-2">
                      <li>
                          <a href="/" class="hover:text-primary">Home</a>
                      </li>
                      <li>
                          <a href="./Services.html" class="hover:text-primary">Services</a>
                      </li>
                      <li>
                          <a href="./About.html" class="hover:text-primary">About</a>
                      </li>
                      <li>
                          <a href="./Contact.html" class="hover:text-primary">Contact</a>
                      </li>
                  </ul>
              </div>
              
              <div>
                  <h3 class="text-xl font-bold mb-4">Contact Us</h3>
                  <ul class="space-y-3">
                      <li class="flex items-start">
                          <i class="fas fa-map-marker-alt h-5 w-5 mr-2 mt-0.5"></i>
                          <span>92 Dorchester Drive, Parkland, Millerton Rural 7441</span>
                      </li>
                      <li class="flex items-center">
                          <i class="fas fa-phone h-5 w-5 mr-2"></i>
                          <span>************</span>
                      </li>
                      <li class="flex items-center">
                          <i class="fas fa-envelope h-5 w-5 mr-2"></i>
                          <span><EMAIL></span>
                      </li>
                  </ul>
              </div>
          </div>
          
          <div class="border-t border-gray-800 mt-12 pt-8 text-center">
              <p>&copy; <span id="current-year"></span> Jane & Peace Enterprises. All rights reserved.</p>
          </div>
      </div>

      <div class="u-clearfix u-sheet u-sheet-1">
        <div class="u-align-center u-clearfix u-custom-html u-expanded-width u-custom-html-1">
          <div id="google_translate_element"></div>
          <script type="text/javascript"> function googleTranslateElementInit() {
  new google.translate.TranslateElement({pageLanguage: 'en'}, 'google_translate_element');
} </script>
          <script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
        </div>
      </div>


  </footer>
    
  
</body></html>