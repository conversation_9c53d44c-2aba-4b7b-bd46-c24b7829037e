 .u-section-1 {
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.45), rgba(0, 0, 0, 0.45)), url('images/pexels-gaion-29735767.jpg');
}

.u-section-1 .u-sheet-1 {
  min-height: 684px;
}

.u-section-1 .u-text-1 {
  text-shadow: 2px 2px 8px rgba(0,0,0,0.4);
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: -300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  font-size: 3rem;
  text-transform: uppercase;
  font-weight: 700;
  width: 795px;
  margin: 197px auto 0;
}

.u-section-1 .u-text-2 {
  text-shadow: 2px 2px 8px rgba(0,0,0,0.4);
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  width: 795px;
  margin: 52px auto 0;
}

.u-section-1 .u-btn-1 {
  background-image: none;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 2px;
  font-size: 1rem;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 52px auto 60px;
  padding: 18px 42px 18px 41px;
}

@media (max-width: 1199px) {
   .u-section-1 {
    background-position: 50% 50%;
    background-size: cover, cover;
  }

  .u-section-1 .u-sheet-1 {
    min-height: 564px;
  }

  .u-section-1 .u-text-1 {
    font-size: 3.4375rem;
    margin-top: 60px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 432px;
  }

  .u-section-1 .u-text-1 {
    font-size: 2.625rem;
    width: 720px;
  }

  .u-section-1 .u-text-2 {
    width: 720px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 324px;
  }

  .u-section-1 .u-text-1 {
    width: 540px;
  }

  .u-section-1 .u-text-2 {
    width: 540px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 204px;
  }

  .u-section-1 .u-text-1 {
    font-size: 2.25rem;
    width: 340px;
  }

  .u-section-1 .u-text-2 {
    width: 340px;
  }
}.u-section-2 .u-sheet-1 {
  min-height: 317px;
}

.u-section-2 .u-text-1 {
  width: 889px;
  margin: 60px auto;
}

@media (max-width: 991px) {
  .u-section-2 .u-text-1 {
    width: 720px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-text-1 {
    width: 540px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-text-1 {
    width: 340px;
  }
} .u-section-3 {
  background-image: none;
}

.u-section-3 .u-sheet-1 {
  min-height: 678px;
}

.u-section-3 .u-shape-1 {
  width: 546px;
  left: 259px;
  right: auto;
  --animation-custom_in-translate_x: -400px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 180deg;
  --animation-custom_in-scale: 1;
  --radius: 20px;
}

.u-section-3 .u-image-1 {
  width: 702px;
  height: 447px;
  --animation-custom_in-translate_x: -400px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 180deg;
  --animation-custom_in-scale: 1;
  --radius: 10px;
  margin: 115px auto 0 0;
}

.u-section-3 .u-group-1 {
  width: 590px;
  min-height: 289px;
  background-image: none;
  height: auto;
  --animation-custom_in-translate_x: -400px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 180deg;
  --animation-custom_in-scale: 1;
  --radius: 10px;
  margin: -368px 0 60px auto;
}

.u-section-3 .u-container-layout-1 {
  padding: 30px;
}

.u-section-3 .u-text-1 {
  font-weight: 700;
  line-height: 1.3;
  font-size: 2.25rem;
  margin: 0;
}

.u-section-3 .u-text-2 {
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-shape-1 {
    right: 192px;
  }

  .u-section-3 .u-group-1 {
    width: 513px;
    height: auto;
  }

  .u-section-3 .u-text-1 {
    font-size: 2rem;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 727px;
  }

  .u-section-3 .u-shape-1 {
    width: 412px;
    left: 134px;
    right: auto;
  }

  .u-section-3 .u-image-1 {
    width: 596px;
    height: 420px;
  }

  .u-section-3 .u-group-1 {
    margin-top: -135px;
    margin-bottom: 38px;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-sheet-1 {
    min-height: 713px;
  }

  .u-section-3 .u-shape-1 {
    left: 128px;
    right: 335px;
  }

  .u-section-3 .u-image-1 {
    width: 467px;
    height: 311px;
    margin-top: 60px;
  }

  .u-section-3 .u-group-1 {
    width: 467px;
    margin-top: -78px;
    margin-bottom: 60px;
  }

  .u-section-3 .u-container-layout-1 {
    padding-left: 25px;
    padding-right: 25px;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-sheet-1 {
    min-height: 905px;
  }

  .u-section-3 .u-shape-1 {
    width: 237px;
    height: 669px;
    left: auto;
    bottom: auto;
    right: auto;
    top: auto;
    margin: 0 auto;
  }

  .u-section-3 .u-image-1 {
    width: 340px;
    height: 226px;
    margin-top: -609px;
    margin-right: 0;
    margin-left: auto;
  }

  .u-section-3 .u-group-1 {
    width: 280px;
    min-height: 589px;
    margin-top: -33px;
    margin-right: auto;
  }

  .u-section-3 .u-container-layout-1 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-3 .u-text-1 {
    font-size: 1.5rem;
  }
} .u-section-4 {
  background-image: none;
}

.u-section-4 .u-sheet-1 {
  min-height: 920px;
}

.u-section-4 .u-text-1 {
  margin: 83px auto 0;
}

.u-section-4 .u-text-2 {
  width: 719px;
  margin: 30px auto 0;
}

.u-section-4 .u-list-1 {
  grid-template-rows: repeat(1, auto);
  margin-top: 40px;
  margin-bottom: 60px;
}

.u-section-4 .u-repeater-1 {
  grid-template-columns: repeat(4, calc(25% - 12px));
  min-height: 574px;
  grid-auto-columns: calc(25% - 12px);
  grid-gap: 16px;
}

.u-section-4 .u-image-1 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.45), rgba(0, 0, 0, 0.45)), url('images/5a13caf8bf17cc20cf99f5d1554c5dde8b3891a738f80a0b726f16779544d4aabe88c1367791bfba6310b1021598600215accf4805552d6993770f_1280.jpg');
  --radius: 15px;
  background-position: 50% 50%;
}

.u-section-4 .u-container-layout-1 {
  padding: 30px 30px 40px;
}

.u-section-4 .u-text-3 {
  margin: auto 0 0;
}

.u-section-4 .u-image-2 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.45), rgba(0, 0, 0, 0.45)), url('images/pexels-vladimirsrajber-11806477.jpg');
  --radius: 15px;
  background-position: 50% 50%;
}

.u-section-4 .u-container-layout-2 {
  padding: 30px 30px 40px;
}

.u-section-4 .u-text-4 {
  margin: auto 0 0;
}

.u-section-4 .u-image-3 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.45), rgba(0, 0, 0, 0.45)), url('images/photo-1740657254989-42fe9c3b8cce.jpeg');
  --radius: 15px;
  background-position: 50% 50%;
  background-size: cover;
}

.u-section-4 .u-container-layout-3 {
  padding: 30px 30px 40px;
}

.u-section-4 .u-text-5 {
  margin: auto 0 0;
}

.u-section-4 .u-image-4 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.45), rgba(0, 0, 0, 0.45)), url('images/855dc0dc97dd938e9e2c7ef8ed19f8bf94f25b9238a8a9aa3e73ddc0e3a33b80f69b3650d70bafe0e03c34c1bf4459300c95beb9d7fd27050f0ab4_1280.jpg');
  background-position: 50% 50%;
  --radius: 15px;
}

.u-section-4 .u-container-layout-4 {
  padding: 30px 30px 40px;
}

.u-section-4 .u-text-6 {
  margin: auto 0 0;
}

.u-section-4 .u-image-5 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.45), rgba(0, 0, 0, 0.45)), url('images/pexels-littlehampton-bricks-2717960-4509089.jpg');
  background-position: 50% 50%;
  --radius: 15px;
}

.u-section-4 .u-container-layout-5 {
  padding: 30px 30px 40px;
}

.u-section-4 .u-text-7 {
  margin: auto 0 0;
}

.u-section-4 .u-image-6 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.45), rgba(0, 0, 0, 0.45)), url('https://cdn.prod.website-files.com/65bab9d72cec00b5fdee3b02/660ebe104c538dc90e2461d0_pest-control-for-bed-bug.png');
  background-position: 50% 50%;
  --radius: 15px;
}

.u-section-4 .u-container-layout-6 {
  padding: 30px 30px 40px;
}

.u-section-4 .u-text-8 {
  margin: auto 0 0;
}

.u-section-4 .u-image-7 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.45), rgba(0, 0, 0, 0.45)), url('https://strongbasements.com/wp-content/uploads/2023/03/Waterproofing-vs.-Plumbing-Calling-the-Right-Professional.jpg');
  background-position: 50% 50%;
  --radius: 15px;
}

.u-section-4 .u-container-layout-7 {
  padding: 30px 30px 40px;
}

.u-section-4 .u-text-9 {
  margin: auto 0 0;
}

.u-section-4 .u-image-8 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.45), rgba(0, 0, 0, 0.45)), url('images/image2.jpg');
  background-position: 50% 50%;
  --radius: 15px;
  background-size: cover;
}

.u-section-4 .u-container-layout-8 {
  padding: 30px 30px 40px;
}

.u-section-4 .u-text-10 {
  margin: auto 0 0;
}

@media (max-width: 1199px) {
  .u-section-4 .u-sheet-1 {
    min-height: 906px;
  }

  .u-section-4 .u-text-1 {
    margin-top: 73px;
  }

  .u-section-4 .u-repeater-1 {
    min-height: 472px;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-text-1 {
    margin-top: 83px;
  }

  .u-section-4 .u-repeater-1 {
    grid-template-columns: repeat(2, calc(50% - 8px));
    min-height: 1488px;
    grid-auto-columns: calc(50% - 8px);
  }

  .u-section-4 .u-container-layout-1 {
    padding-bottom: 30px;
  }

  .u-section-4 .u-container-layout-2 {
    padding-bottom: 30px;
  }

  .u-section-4 .u-container-layout-3 {
    padding-bottom: 30px;
  }

  .u-section-4 .u-container-layout-4 {
    padding-bottom: 30px;
  }

  .u-section-4 .u-container-layout-5 {
    padding-bottom: 30px;
  }

  .u-section-4 .u-container-layout-6 {
    padding-bottom: 30px;
  }

  .u-section-4 .u-container-layout-7 {
    padding-bottom: 30px;
  }

  .u-section-4 .u-container-layout-8 {
    padding-bottom: 30px;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-text-1 {
    margin-top: 60px;
  }

  .u-section-4 .u-text-2 {
    width: 540px;
  }

  .u-section-4 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-7 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-8 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-sheet-1 {
    min-height: 2450px;
  }

  .u-section-4 .u-text-1 {
    margin-top: 10px;
  }

  .u-section-4 .u-text-2 {
    width: 340px;
  }

  .u-section-4 .u-repeater-1 {
    grid-template-columns: 100%;
    min-height: 2102px;
    grid-auto-columns: calc(100% + 0px);
  }

  .u-section-4 .u-text-3 {
    margin-top: 165px;
  }

  .u-section-4 .u-text-4 {
    margin-top: 165px;
  }

  .u-section-4 .u-text-5 {
    margin-top: 165px;
  }

  .u-section-4 .u-text-6 {
    margin-top: 165px;
  }

  .u-section-4 .u-text-7 {
    margin-top: 165px;
  }

  .u-section-4 .u-text-8 {
    margin-top: 165px;
  }

  .u-section-4 .u-text-9 {
    margin-top: 165px;
  }

  .u-section-4 .u-text-10 {
    margin-top: 165px;
  }
}.u-section-5 .u-sheet-1 {
  min-height: 973px;
}

.u-section-5 .u-layout-wrap-1 {
  margin-top: 100px;
  margin-bottom: 30px;
}

.u-section-5 .u-layout-cell-1 {
  min-height: 843px;
}

.u-section-5 .u-container-layout-1 {
  padding: 30px;
}

.u-section-5 .u-text-1 {
  font-size: 3rem;
  margin: 0 85px 0 0;
}

.u-section-5 .u-list-1 {
  margin-top: 27px;
  margin-bottom: 0;
}

.u-section-5 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 519px;
  grid-gap: 30px;
}

.u-section-5 .u-list-item-1 {
  --radius: 30px;
}

.u-section-5 .u-container-layout-2 {
  padding: 28px 30px;
}

.u-section-5 .u-icon-1 {
  width: 36px;
  height: 36px;
  margin: 0 490px 0 auto;
}

.u-section-5 .u-text-2 {
  margin: -36px auto 0 56px;
}

.u-section-5 .u-text-3 {
  margin: 11px 0 0 56px;
}

.u-section-5 .u-list-item-2 {
  --radius: 30px;
}

.u-section-5 .u-container-layout-3 {
  padding: 28px 30px;
}

.u-section-5 .u-icon-2 {
  width: 36px;
  height: 36px;
  margin: 0 490px 0 auto;
}

.u-section-5 .u-text-4 {
  margin: -36px auto 0 56px;
}

.u-section-5 .u-text-5 {
  margin: 11px 0 0 56px;
}

.u-section-5 .u-list-item-3 {
  --radius: 30px;
}

.u-section-5 .u-container-layout-4 {
  padding: 28px 30px;
}

.u-section-5 .u-icon-3 {
  width: 36px;
  height: 36px;
  margin: 0 490px 0 auto;
}

.u-section-5 .u-text-6 {
  margin: -36px auto 0 56px;
}

.u-section-5 .u-text-7 {
  margin: 11px 0 0 56px;
}

.u-section-5 .u-image-1 {
  min-height: 419px;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url("images/pexels-rquiros-2219024.jpg");
  background-size: cover;
  --radius: 50px;
}

.u-section-5 .u-container-layout-5 {
  padding: 30px;
}

@media (max-width: 1199px) {
  .u-section-5 .u-sheet-1 {
    min-height: 783px;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 663px;
  }

  .u-section-5 .u-container-layout-1 {
    padding-left: 29px;
    padding-right: 29px;
  }

  .u-section-5 .u-text-1 {
    margin-right: 52px;
    margin-left: 1px;
  }

  .u-section-5 .u-list-1 {
    margin-top: 148px;
  }

  .u-section-5 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-5 .u-container-layout-2 {
    padding-bottom: 27px;
  }

  .u-section-5 .u-icon-1 {
    margin-right: 386px;
  }

  .u-section-5 .u-text-2 {
    width: auto;
    margin-right: 66px;
    margin-left: 60px;
  }

  .u-section-5 .u-text-3 {
    width: auto;
    margin-top: 20px;
    margin-right: 60px;
    margin-left: 0;
  }

  .u-section-5 .u-container-layout-3 {
    padding-bottom: 27px;
  }

  .u-section-5 .u-icon-2 {
    margin-right: 386px;
  }

  .u-section-5 .u-text-4 {
    width: auto;
    margin-right: 66px;
    margin-left: 60px;
  }

  .u-section-5 .u-text-5 {
    width: auto;
    margin-top: 20px;
    margin-right: 60px;
    margin-left: 0;
  }

  .u-section-5 .u-container-layout-4 {
    padding-bottom: 27px;
  }

  .u-section-5 .u-icon-3 {
    margin-right: 386px;
  }

  .u-section-5 .u-text-6 {
    width: auto;
    margin-right: 66px;
    margin-left: 60px;
  }

  .u-section-5 .u-text-7 {
    width: auto;
    margin-top: 20px;
    margin-right: 60px;
    margin-left: 0;
  }

  .u-section-5 .u-image-1 {
    min-height: 663px;
  }
}

@media (max-width: 991px) {
  .u-section-5 .u-sheet-1 {
    min-height: 1131px;
  }

  .u-section-5 .u-layout-wrap-1 {
    width: 693px;
    margin: 14px auto 37px 0;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 1080px;
  }

  .u-section-5 .u-container-layout-1 {
    padding-bottom: 0;
  }

  .u-section-5 .u-text-1 {
    font-size: 2.25rem;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-5 .u-list-1 {
    margin-top: 26px;
  }

  .u-section-5 .u-repeater-1 {
    min-height: 914px;
  }

  .u-section-5 .u-container-layout-2 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .u-section-5 .u-icon-1 {
    margin-top: 30px;
    margin-right: 294px;
  }

  .u-section-5 .u-text-2 {
    margin-top: -30px;
    margin-right: 17px;
    margin-left: 56px;
  }

  .u-section-5 .u-text-3 {
    margin-top: 36px;
    margin-right: 0;
  }

  .u-section-5 .u-container-layout-3 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .u-section-5 .u-icon-2 {
    margin-top: 30px;
    margin-right: 294px;
  }

  .u-section-5 .u-text-4 {
    margin-top: -30px;
    margin-right: 17px;
    margin-left: 56px;
  }

  .u-section-5 .u-text-5 {
    margin-top: 36px;
    margin-right: 0;
  }

  .u-section-5 .u-container-layout-4 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .u-section-5 .u-icon-3 {
    margin-top: 30px;
    margin-right: 294px;
  }

  .u-section-5 .u-text-6 {
    margin-top: -30px;
    margin-right: 17px;
    margin-left: 56px;
  }

  .u-section-5 .u-text-7 {
    margin-top: 36px;
    margin-right: 0;
  }

  .u-section-5 .u-image-1 {
    min-height: 1080px;
  }
}

@media (max-width: 767px) {
  .u-section-5 .u-sheet-1 {
    min-height: 1372px;
  }

  .u-section-5 .u-layout-wrap-1 {
    margin-top: -363px;
    margin-bottom: -363px;
    width: 540px;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 666px;
  }

  .u-section-5 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-list-1 {
    margin-top: 139px;
  }

  .u-section-5 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-icon-1 {
    margin-right: 464px;
  }

  .u-section-5 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-icon-2 {
    margin-right: 464px;
  }

  .u-section-5 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-icon-3 {
    margin-right: 464px;
  }

  .u-section-5 .u-image-1 {
    min-height: 586px;
  }

  .u-section-5 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-5 .u-sheet-1 {
    min-height: 1379px;
  }

  .u-section-5 .u-layout-wrap-1 {
    margin-top: -67px;
    margin-bottom: 13px;
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 1069px;
  }

  .u-section-5 .u-container-layout-1 {
    padding: 15px 0 30px;
  }

  .u-section-5 .u-text-1 {
    font-size: 1.875rem;
    width: auto;
    margin-top: 1px;
    margin-left: 10px;
    margin-right: 10px;
  }

  .u-section-5 .u-list-1 {
    width: 320px;
    margin-top: 22px;
    margin-right: 0;
    margin-left: auto;
  }

  .u-section-5 .u-container-layout-2 {
    padding: 20px;
  }

  .u-section-5 .u-icon-1 {
    margin-top: 0;
    margin-right: 281px;
  }

  .u-section-5 .u-text-2 {
    margin-top: 20px;
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-5 .u-text-3 {
    margin-top: 15px;
  }

  .u-section-5 .u-container-layout-3 {
    padding: 20px;
  }

  .u-section-5 .u-icon-2 {
    margin-top: 0;
    margin-right: 281px;
  }

  .u-section-5 .u-text-4 {
    margin-top: 20px;
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-5 .u-text-5 {
    margin-top: 15px;
  }

  .u-section-5 .u-container-layout-4 {
    padding: 20px;
  }

  .u-section-5 .u-icon-3 {
    margin-top: 0;
    margin-right: 281px;
  }

  .u-section-5 .u-text-6 {
    margin-top: 20px;
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-5 .u-text-7 {
    margin-top: 15px;
  }

  .u-section-5 .u-image-1 {
    min-height: 364px;
  }
}.u-section-6 .u-sheet-1 {
  min-height: 602px;
}

.u-section-6 .u-text-1 {
  margin: 19px auto 0;
}

.u-section-6 .u-list-1 {
  margin-bottom: 50px;
  margin-top: 39px;
}

.u-section-6 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 13.3333px);
  grid-template-columns: repeat(3, calc(33.3333% - 13.3333px));
  min-height: 443px;
  grid-gap: 20px;
}

.u-section-6 .u-list-item-1 {
  --radius: 30px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-6 .u-container-layout-1 {
  padding: 30px;
}

.u-section-6 .u-text-2 {
  font-size: 2.25rem;
  margin: 0;
}

.u-section-6 .u-text-3 {
  margin: 21px 0 0;
}

.u-section-6 .u-text-4 {
  margin: 133px 0 0;
}

.u-section-6 .u-list-item-2 {
  --radius: 30px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-6 .u-container-layout-2 {
  padding: 30px;
}

.u-section-6 .u-text-5 {
  font-size: 2.25rem;
  margin: 0;
}

.u-section-6 .u-text-6 {
  margin: 21px 0 0;
}

.u-section-6 .u-text-7 {
  margin: 133px 0 0;
}

.u-section-6 .u-list-item-3 {
  --radius: 30px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-6 .u-container-layout-3 {
  padding: 30px;
}

.u-section-6 .u-text-8 {
  font-size: 2.25rem;
  margin: 0;
}

.u-section-6 .u-text-9 {
  margin: 21px 0 0;
}

.u-section-6 .u-text-10 {
  margin: 133px 0 0;
}

@media (max-width: 1199px) {
  .u-section-6 .u-list-1 {
    margin-bottom: -13px;
  }

  .u-section-6 .u-repeater-1 {
    min-height: 472px;
  }

  .u-section-6 .u-container-layout-1 {
    padding-bottom: 20px;
  }

  .u-section-6 .u-text-2 {
    font-size: 1.875rem;
  }

  .u-section-6 .u-text-5 {
    font-size: 1.875rem;
  }

  .u-section-6 .u-text-8 {
    font-size: 1.875rem;
  }
}

@media (max-width: 991px) {
  .u-section-6 .u-sheet-1 {
    min-height: 1218px;
  }

  .u-section-6 .u-repeater-1 {
    grid-auto-columns: calc(50% - 9.999975px);
    grid-template-columns: repeat(2, calc(50% - 9.999975px));
    min-height: 1121px;
  }
}

@media (max-width: 767px) {
  .u-section-6 .u-list-1 {
    margin-bottom: 50px;
  }

  .u-section-6 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-6 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-6 .u-text-2 {
    width: auto;
  }

  .u-section-6 .u-text-3 {
    width: auto;
  }

  .u-section-6 .u-text-4 {
    width: auto;
  }

  .u-section-6 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 20px;
  }

  .u-section-6 .u-text-5 {
    width: auto;
  }

  .u-section-6 .u-text-6 {
    width: auto;
  }

  .u-section-6 .u-text-7 {
    width: auto;
  }

  .u-section-6 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 20px;
  }

  .u-section-6 .u-text-8 {
    width: auto;
  }

  .u-section-6 .u-text-9 {
    width: auto;
  }

  .u-section-6 .u-text-10 {
    width: auto;
  }
}

@media (max-width: 575px) {
  .u-section-6 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-6 .u-text-2 {
    font-size: 1.5rem;
  }

  .u-section-6 .u-text-5 {
    font-size: 1.5rem;
  }

  .u-section-6 .u-text-8 {
    font-size: 1.5rem;
  }
}.u-section-7 .u-sheet-1 {
  min-height: 636px;
}

.u-section-7 .u-layout-wrap-1 {
  margin-top: 1px;
  margin-bottom: 40px;
}

.u-section-7 .u-layout-cell-1 {
  min-height: 595px;
  --top-left-radius: 20px;
}

.u-section-7 .u-container-layout-1 {
  padding: 30px 30px 29px 0;
}

.u-section-7 .u-image-1 {
  height: 535px;
  object-position: 78.48% 0.15%;
  --radius: 10px;
  width: 519px;
  margin: 0 auto 0 0;
}

.u-section-7 .u-layout-cell-2 {
  min-height: 595px;
  --animation-custom_in-translate_x: 300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-7 .u-container-layout-2 {
  padding: 30px 0 30px 40px;
}

.u-section-7 .u-text-1 {
  font-size: 3rem;
  margin: 0 auto 0 0;
}

.u-section-7 .u-text-2 {
  margin: 17px auto 0 0;
}

.u-section-7 .u-btn-1 {
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 1px;
  margin: 30px auto 0 0;
  padding: 19px 49px 19px 47px;
}

.u-section-7 .u-icon-1 {
  font-size: 1.2858em;
  margin-left: 0;
  vertical-align: -1px;
}

@media (max-width: 1199px) {
  .u-section-7 .u-sheet-1 {
    min-height: 504px;
  }

  .u-section-7 .u-layout-cell-1 {
    min-height: 733px;
  }

  .u-section-7 .u-image-1 {
    width: 416px;
    height: 511px;
  }

  .u-section-7 .u-layout-cell-2 {
    min-height: 491px;
  }

  .u-section-7 .u-btn-1 {
    --radius: 20px;
  }
}

@media (max-width: 991px) {
  .u-section-7 .u-sheet-1 {
    min-height: 230px;
  }

  .u-section-7 .u-layout-cell-1 {
    min-height: 728px;
  }

  .u-section-7 .u-image-1 {
    width: 594px;
    height: 605px;
    margin-left: auto;
  }

  .u-section-7 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-7 .u-container-layout-2 {
    padding-right: 30px;
    padding-left: 0;
  }

  .u-section-7 .u-text-1 {
    width: auto;
    margin-left: 47px;
    margin-right: 47px;
  }

  .u-section-7 .u-text-2 {
    width: auto;
    margin-right: 0;
  }

  .u-section-7 .u-btn-1 {
    margin-left: auto;
  }
}

@media (max-width: 767px) {
  .u-section-7 .u-sheet-1 {
    min-height: 330px;
  }

  .u-section-7 .u-layout-cell-1 {
    min-height: 546px;
  }

  .u-section-7 .u-container-layout-1 {
    padding-right: 10px;
  }

  .u-section-7 .u-image-1 {
    width: 461px;
    height: 544px;
  }

  .u-section-7 .u-text-1 {
    font-size: 2.25rem;
  }
}

@media (max-width: 575px) {
  .u-section-7 .u-layout-cell-1 {
    min-height: 344px;
  }

  .u-section-7 .u-image-1 {
    width: 301px;
    height: 358px;
  }

  .u-section-7 .u-container-layout-2 {
    padding-right: 10px;
  }

  .u-section-7 .u-text-1 {
    font-size: 1.875rem;
  }
}.u-section-8 .u-sheet-1 {
  min-height: 688px;
}

.u-section-8 .u-text-1 {
  font-size: 3.75rem;
  background-image: none;
  font-weight: 700;
  letter-spacing: 4px;
  text-transform: none;
  margin: 14px 570px 0 0;
}

.u-section-8 .u-accordion-1 {
  margin: 50px 0 5px auto;
}

.u-section-8 .u-accordion-link-1 {
  font-weight: 700;
  padding: 20px 30px;
}

.u-section-8 .u-icon-1 {
  height: 20px;
  width: 20px;
  background-image: none;
}

.u-section-8 .u-accordion-pane-1 {
  min-height: 150px;
}

.u-section-8 .u-container-layout-1 {
  padding: 20px 30px;
}

.u-section-8 .u-accordion-link-2 {
  font-weight: 700;
  padding: 20px 30px;
}

.u-section-8 .u-icon-2 {
  height: 20px;
  width: 20px;
  background-image: none;
}

.u-section-8 .u-accordion-pane-2 {
  min-height: 150px;
}

.u-section-8 .u-container-layout-2 {
  padding: 20px 30px;
}

.u-section-8 .u-accordion-link-3 {
  font-weight: 700;
  padding: 20px 30px;
}

.u-section-8 .u-icon-3 {
  height: 20px;
  width: 20px;
  background-image: none;
}

.u-section-8 .u-accordion-pane-3 {
  min-height: 150px;
}

.u-section-8 .u-container-layout-3 {
  padding: 20px 30px;
}

.u-section-8 .u-accordion-link-4 {
  font-weight: 700;
  padding: 20px 30px;
}

.u-section-8 .u-icon-4 {
  height: 20px;
  width: 20px;
  background-image: none;
}

.u-section-8 .u-accordion-pane-4 {
  min-height: 150px;
}

.u-section-8 .u-container-layout-4 {
  padding: 20px 30px;
}

@media (max-width: 1199px) {
  .u-section-8 .u-text-1 {
    width: 570px;
  }

  .u-section-8 .u-accordion-1 {
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 991px) {
  .u-section-8 .u-text-1 {
    margin-right: 350px;
  }
}

@media (max-width: 767px) {
  .u-section-8 .u-sheet-1 {
    min-height: 789px;
  }

  .u-section-8 .u-text-1 {
    width: 540px;
    margin-right: 170px;
  }

  .u-section-8 .u-accordion-1 {
    margin-bottom: 40px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-8 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-8 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-8 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-8 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-8 .u-sheet-1 {
    min-height: 829px;
  }

  .u-section-8 .u-text-1 {
    font-size: 3rem;
    width: auto;
    margin-right: 0;
  }

  .u-section-8 .u-accordion-1 {
    margin-bottom: 80px;
    margin-right: initial;
    margin-left: initial;
  }
}