<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <!-- A single viewport meta tag -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Peace And Jane- Receipt</title>
  <style>
    /* Basic Reset */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    /* Body Styling */
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f4f4f4;
      padding: 20px;
    }

    /* Fluid Container */
    .container {
      width: 100%;
      max-width: 700px; /* Reduced from 800px */
      margin: 0 auto;   /* Center horizontally */
      background: white;
      padding: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    /* Header */
    .header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;
    }

    .logo {
      width: 100px;
      height: 100px;
      background-image: url('../images/logo.png');
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
    }

    h1 {
      text-align: center;
      flex-grow: 1;
      margin: 0 20px;
      font-size: 32px;
    }

    /* Company Details */
    .company-details {
      margin-bottom: 20px;
    }

    .company-details h2 {
      margin-bottom: 10px;
      font-size: 24px;
    }

    .address-contact {
      margin-bottom: 10px;
    }

    /* Reference Number */
    .reference-number {
      margin-top: 10px;
      font-size: 16px;
      font-weight: bold;
    }

    /* Billing and Shipping */
    .billing-shipping {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .billing, .shipping {
      flex-basis: 48%;
      margin-bottom: 20px;
    }

    .billing h3, .shipping h3 {
      margin-bottom: 10px;
      font-size: 20px;
      border-bottom: 1px solid #ddd;
      padding-bottom: 5px;
    }

    /* Payment Method Section */
    .payment-method {
      margin-bottom: 20px;
      font-size: 16px;
    }

    /* Table Styling */
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
      table-layout: fixed;
    }

    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
      word-wrap: break-word;
    }

    th {
      background-color: #f2f2f2;
      font-size: 16px;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    /* Ensure all table headers and data cells within #receipt are left-aligned */
    #receipt table th,
    #receipt table td {
      text-align: left;
    }

    /* Additional Comments Section */
    .additional-comments {
      margin-bottom: 20px;
    }

    .additional-comments h3 {
      margin-bottom: 10px;
      font-size: 20px;
    }

    .additional-comments p {
      font-size: 16px;
      white-space: pre-wrap;
    }

    /* Terms Section */
    .terms {
      text-align: left;
      margin-bottom: 20px;
    }

    .terms h3 {
      margin-bottom: 5px;
      font-size: 16px;
    }

    .terms p {
      font-size: 14px;
    }

    /* Totals Section */
    .totals {
      margin-top: 30px;
      padding: 20px;
      background-color: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 5px;
    }

    .totals p {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      margin-bottom: 10px;
    }

    .totals p span {
      text-align: right;
      font-weight: bold;
    }

    .totals p.tax {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      margin-bottom: 10px;
    }

    .totals p.tax span {
      text-align: right;
      font-weight: bold;
    }

    /* Footer */
    .footer {
      text-align: center;
      color: #666;
      font-size: 14px;
      margin-top: 30px;
    }

    /* Button Styles */
    .button-container {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin-top: 20px;
    }

    .action-button {
      background-color: #4a90e2;
      color: white;
      border: none;
      border-radius: 5px;
      padding: 10px 20px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .action-button:hover {
      background-color: #357ABD;
    }

    .action-button:disabled {
      background-color: #aaa;
      cursor: not-allowed;
    }

    /* Toast Container & Toast Styles */
    .toast-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .toast {
      background-color: #333;
      color: #fff;
      padding: 10px 15px;
      border-radius: 5px;
      font-size: 14px;
      opacity: 0.95;
      animation: fadeInOut 3s forwards;
    }

    .toast.success {
      background-color: #28a745;
    }

    .toast.error {
      background-color: #dc3545;
    }

    .toast.info {
      background-color: #007bff;
    }

    @keyframes fadeInOut {
      0% { opacity: 0; }
      10% { opacity: 0.95; }
      90% { opacity: 0.95; }
      100% { opacity: 0; }
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
      .billing, .shipping {
        flex-basis: 100%;
      }
      h1 {
        font-size: 28px;
      }
      .company-details h2 {
        font-size: 22px;
      }
      .billing h3, .shipping h3 {
        font-size: 18px;
      }
      .additional-comments h3 {
        font-size: 18px;
      }
      .additional-comments p {
        font-size: 14px;
      }
      .terms h3 {
        font-size: 14px;
      }
      .terms p {
        font-size: 12px;
      }
      .totals {
        padding: 15px;
      }
      .totals p {
        font-size: 14px;
      }
      table, th, td {
        font-size: 14px;
      }
    }

    @media (max-width: 480px) {
      h1 {
        font-size: 24px;
      }
      .company-details h2 {
        font-size: 20px;
      }
      .billing h3, .shipping h3 {
        font-size: 16px;
      }
      .totals p {
        font-size: 12px;
      }
      table, th, td {
        font-size: 12px;
        padding: 8px 10px;
      }
    }
  </style>
</head>
<body>
  <!-- Toast Container -->
  <div class="toast-container" id="toastContainer"></div>

  <a href="index.html" style="text-decoration: none; color: #007BFF; font-size: 16px; margin-right: 20px;">&larr; Back to Home</a>
  <div class="container">
    <div id="receipt">
      <div class="header">
        <p id="receipt-date">Date: <!-- Dynamic Date --></p>
        <h1>RECEIPT</h1>
        <div class="logo"></div>
      </div>

      <div class="company-details">
        <h2 id="company-name">Peace and Jane pty ltd</h2>
        <p>Cape Town and East London</p>
        <div class="address-contact">
          <p id="banking-info"></p>
        </div>
        <p class="reference-number" id="reference-number">Reference Number: <!-- Dynamic Reference --></p>
      </div>

      <div class="billing-shipping">
        <div class="billing">
          <h3>BILL TO</h3>
          <p><!-- Customer Billing Information --></p>
        </div>
        <div class="shipping">
          <h3>SHIP TO</h3>
          <p><!-- Customer Shipping Information --></p>
        </div>
      </div>

      <p class="payment-method">Payment Method: <span id="paymentMethod">N/A</span></p>

      <!-- Products Table -->
      <table>
        <thead>
          <tr id="table-header">
            <th>Product</th>
            <th>QTY</th>
            <th>UNIT PRICE (R)</th>
            <th>TAX PER UNIT (R)</th>
            <th>TOTAL (R)</th>
          </tr>
        </thead>
        <tbody>
          <!-- Dynamic Product Rows Will Be Inserted Here -->
        </tbody>
      </table>

      <!-- Additional Comments Section -->
      <div id="additional-comments" class="additional-comments" style="margin-bottom: 20px;">
        <!-- Additional Comments will be inserted here if available -->
      </div>

      <!-- Terms Section -->
      <div class="terms">
        <h3>Terms</h3>
        <p>No refunds or exchanges on correctly supplied items</p>
      </div>

      <!-- Totals Section -->
      <div class="totals">
        <p class="subtotal">Product Amount (Subtotal): <span id="subtotalAmount">R0.00</span></p>
        <p class="tax" id="tax-info" style="display: none;">Tax (15%): <span id="taxAmount">R0.00</span></p>
        <p class="total">Total Amount: <span id="totalAmount">R0.00</span></p>
      </div>
    </div>

  </div>

  <div class="button-container">
    <button id="generate-pdf-button" class="action-button">Generate PDF</button>
  </div>

  <div id="pdf-preview"></div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

  <script>
    /**
     * Display a non-blocking toast message in the top-right corner.
     * @param {string} message - The message to display
     * @param {string} [type='info'] - The type of toast ('success', 'error', 'info')
     */
    function showToastMessage(message, type = 'info') {
      const toastContainer = document.getElementById('toastContainer');
      const toast = document.createElement('div');
      toast.classList.add('toast', type);
      toast.textContent = message;
      toastContainer.appendChild(toast);
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }

    // Function to retrieve a cookie by name
    function getCookie(name) {
      const cname = name + "=";
      const decodedCookie = decodeURIComponent(document.cookie);
      const ca = decodedCookie.split(';');
      for (let i = 0; i < ca.length; i++) {
        let c = ca[i].trim();
        if (c.indexOf(cname) === 0) {
          return c.substring(cname.length, c.length);
        }
      }
      return "";
    }

    // Function to format currency in ZAR
    function formatCurrency(amount) {
      return 'R' + parseFloat(amount).toLocaleString('en-ZA', { 
        minimumFractionDigits: 2, 
        maximumFractionDigits: 2 
      });
    }

    // Function to sanitize HTML to prevent XSS
    function sanitizeHTML(str) {
      const temp = document.createElement('div');
      temp.textContent = str;
      return temp.innerHTML;
    }

    // Function to format date
    function formatDate(date) {
      const options = { year: 'numeric', month: 'short', day: 'numeric' };
      return new Intl.DateTimeFormat('en-US', options).format(date);
    }

    // Function to generate a random 7-digit reference number
    function generateReferenceNumber() {
      return Math.floor(1000000 + Math.random() * 9000000).toString();
    }

    /**
     * Populate the receipt with data from cookies, applying the "tax = 15% of final price" logic.
     */
    function populateReceipt() {
      const customerInfoCookie = getCookie('customerInfo');
      const selectedProductsCookie = getCookie('selectedProducts');
      const subtotalAmountCookie = getCookie('subtotalAmount');
      const taxAmountCookie = getCookie('taxAmount');
      const totalAmountCookie = getCookie('totalAmount');
      const selectedCompanyCookie = getCookie('selectedCompany');

      if (!customerInfoCookie ||
          !selectedProductsCookie ||
          !subtotalAmountCookie ||
          !taxAmountCookie ||
          !totalAmountCookie ||
          !selectedCompanyCookie) {
        showToastMessage('No receipt data found. Please generate a receipt first.', 'error');
        setTimeout(() => {
          window.location.href = 'index.html';
        }, 1500);
        return;
      }

      const customerInfo = JSON.parse(customerInfoCookie);
      const selectedProducts = JSON.parse(selectedProductsCookie);
      const subtotalAmount = parseFloat(JSON.parse(subtotalAmountCookie));
      const taxAmount = parseFloat(JSON.parse(taxAmountCookie));
      const totalAmount = parseFloat(JSON.parse(totalAmountCookie));
      const selectedCompany = JSON.parse(selectedCompanyCookie);

      document.getElementById('company-name').textContent = selectedCompany.name;
      document.getElementById('banking-info').innerHTML = selectedCompany.bankingInformation;

      const referenceNumberElement = document.getElementById('reference-number');
      const generatedReference = generateReferenceNumber();
      referenceNumberElement.textContent = 'Reference Number: ' + generatedReference;

      // Populate Billing
      const billingElement = document.querySelector('.billing p');
      billingElement.innerHTML = `
        <strong>${sanitizeHTML(customerInfo.billing.name)}</strong><br>
        ${sanitizeHTML(customerInfo.billing.address)}<br>
        Email: ${sanitizeHTML(customerInfo.billing.email)}<br>
        Phone: ${sanitizeHTML(customerInfo.billing.phone)}
      `;

      // Populate Shipping
      const shippingElement = document.querySelector('.shipping p');
      if (customerInfo.shipping) {
        shippingElement.innerHTML = `
          <strong>${sanitizeHTML(customerInfo.shipping.name)}</strong><br>
          ${sanitizeHTML(customerInfo.shipping.address)}<br>
          Email: ${sanitizeHTML(customerInfo.shipping.email)}<br>
          Phone: ${sanitizeHTML(customerInfo.shipping.phone)}
        `;
      } else {
        shippingElement.innerHTML = `Same as billing address`;
      }

      // Determine if tax is actually added by checking if taxAmount > 0
      const isTaxAdded = (taxAmount > 0);
      const tableHeaderRow = document.getElementById('table-header');
      const taxInfo = document.getElementById('tax-info');

      // If there's any tax, show the "TAX PER UNIT" column, otherwise remove it
      if (isTaxAdded) {
        // Make sure the 4th column is "TAX PER UNIT"
        const possibleTaxHeader = tableHeaderRow.querySelector('th:nth-child(4)');
        if (!possibleTaxHeader || !possibleTaxHeader.textContent.includes('TAX PER UNIT')) {
          const taxHeader = document.createElement('th');
          taxHeader.textContent = 'TAX PER UNIT (R)';
          tableHeaderRow.insertBefore(taxHeader, tableHeaderRow.children[3]);
        }
        taxInfo.style.display = 'flex';
      } else {
        // If there's a "TAX PER UNIT" header, remove it
        const taxHeader = tableHeaderRow.querySelector('th:nth-child(4)');
        if (taxHeader && taxHeader.textContent.includes('TAX PER UNIT')) {
          taxHeader.parentNode.removeChild(taxHeader);
        }
        taxInfo.style.display = 'none';
      }

      // Now populate the table rows
      const tbody = document.querySelector('table tbody');
      tbody.innerHTML = '';

      selectedProducts.forEach(product => {
        const row = tbody.insertRow();

        // Product name
        const descCell = row.insertCell(0);
        descCell.textContent = product.name;

        // Quantity
        const qtyCell = row.insertCell(1);
        qtyCell.textContent = product.quantity;

        // We'll apply the same "tax = 15% of final price" logic used in the main code
        if (isTaxAdded) {
          // net = price * 0.85, tax = price * 0.15, total = price * quantity
          const netPrice = product.price * 0.85;
          const taxPerUnit = product.price * 0.15;
          const lineTotal = product.price * product.quantity;

          // Unit Price cell
          const unitPriceCell = row.insertCell(2);
          unitPriceCell.textContent = formatCurrency(netPrice.toFixed(2));

          // Tax cell
          const taxCell = row.insertCell(3);
          taxCell.textContent = formatCurrency(taxPerUnit.toFixed(2));

          // Total cell
          const totalCell = row.insertCell(4);
          totalCell.textContent = formatCurrency(lineTotal.toFixed(2));
        } else {
          // No tax => unit price is the full product.price
          // total = price * quantity
          const lineTotal = product.price * product.quantity;

          // Unit Price cell
          const unitPriceCell = row.insertCell(2);
          unitPriceCell.textContent = formatCurrency(product.price.toFixed(2));

          // Then the 4th cell is "Total" (since no separate tax column)
          const totalCell = row.insertCell(3);
          totalCell.textContent = formatCurrency(lineTotal.toFixed(2));
        }
      });

      // Additional Comments
      const comments = customerInfo.comments;
      const commentsDiv = document.getElementById('additional-comments');
      if (comments && comments.trim() !== "") {
        commentsDiv.innerHTML = `
          <h3>Additional Comments:</h3>
          <p>${sanitizeHTML(comments).replace(/\n/g, '<br>')}</p>
        `;
        commentsDiv.style.display = 'block';
      } else {
        commentsDiv.style.display = 'none';
      }

      // Fill in the Subtotal, Tax, and Total
      document.getElementById('subtotalAmount').textContent = formatCurrency(subtotalAmount.toFixed(2));
      document.getElementById('taxAmount').textContent = formatCurrency(taxAmount.toFixed(2));
      document.getElementById('totalAmount').textContent = formatCurrency(totalAmount.toFixed(2));

      // Payment Method
      const paymentMethodSpan = document.getElementById('paymentMethod');
      paymentMethodSpan.textContent = customerInfo.paymentMethod ? sanitizeHTML(customerInfo.paymentMethod) : 'N/A';

      // Date
      const dateElement = document.getElementById('receipt-date');
      dateElement.textContent = 'Date: ' + formatDate(new Date());
    }

    // Generate PDF targeting #receipt, with margins in points so it's centered
    function generatePDF() {
      const element = document.getElementById('receipt');
      const opt = {
        // ~ 1 inch margins on each side
        margin: [40, 40, 40, 40],
        filename: 'receipt.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2, scrollY: 0 },
        jsPDF: { unit: 'pt', format: 'a4', orientation: 'portrait' },
        pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
      };
      html2pdf().set(opt).from(element).save().then(() => {
        showToastMessage('PDF generated successfully!', 'success');
      }).catch((error) => {
        console.error('Error generating PDF:', error);
        showToastMessage('Error generating PDF.', 'error');
      });
    }

    document.addEventListener('DOMContentLoaded', populateReceipt);
    document.getElementById('generate-pdf-button').addEventListener('click', generatePDF);
  </script>
</body>
</html>
