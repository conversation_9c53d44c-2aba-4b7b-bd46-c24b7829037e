 .u-section-1 {
  background-image: none;
}

.u-section-1 .u-sheet-1 {
  min-height: 349px;
}

.u-section-1 .u-layout-wrap-1 {
  width: 1190px;
  margin: 15px auto;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 319px;
  --animation-custom_in-translate_x: -300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-1 .u-container-layout-1 {
  padding: 30px 40px;
}

.u-section-1 .u-text-1 {
  font-weight: 700;
  font-size: 3.75rem;
  --animation-custom_in-translate_x: 300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 7px 0 0;
}

.u-section-1 .u-text-2 {
  margin: 27px 0 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 282px;
  }

  .u-section-1 .u-layout-wrap-1 {
    width: 940px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 252px;
  }

  .u-section-1 .u-text-1 {
    line-height: 1;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 130px;
  }

  .u-section-1 .u-layout-wrap-1 {
    width: calc(((100% - 720px) / 2)  + 720px);
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1 .u-container-layout-1 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-1 .u-text-1 {
    font-size: 3.5625rem;
    margin-top: 152px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-layout-wrap-1 {
    width: 540px;
  }

  .u-section-1 .u-container-layout-1 {
    padding-right: 50px;
    padding-left: 10px;
  }

  .u-section-1 .u-text-1 {
    margin-top: 106px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-layout-wrap-1 {
    width: 390px;
  }

  .u-section-1 .u-container-layout-1 {
    padding-top: 65px;
    padding-right: 10px;
  }

  .u-section-1 .u-text-1 {
    font-size: 3rem;
    width: auto;
    margin-top: 15px;
    margin-right: 40px;
  }

  .u-section-1 .u-text-2 {
    width: auto;
    margin-top: 24px;
  }
} .u-section-2 {
  background-image: none;
}

.u-section-2 .u-sheet-1 {
  min-height: 524px;
}

.u-section-2 .u-text-1 {
  margin: 60px auto 0 0;
}

.u-section-2 .u-text-2 {
  margin: 19px 210px 0 0;
}

.u-section-2 .u-text-3 {
  margin: 71px 210px 0 0;
}

.u-section-2 .u-text-4 {
  margin: 20px 210px 60px 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-text-2 {
    margin-right: 10px;
  }

  .u-section-2 .u-text-3 {
    margin-right: 10px;
  }

  .u-section-2 .u-text-4 {
    margin-right: 10px;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-text-2 {
    margin-right: 0;
  }

  .u-section-2 .u-text-3 {
    margin-right: 0;
  }

  .u-section-2 .u-text-4 {
    margin-right: 0;
  }
} .u-section-3 {
  background-image: none;
}

.u-section-3 .u-sheet-1 {
  min-height: 880px;
}

.u-section-3 .u-text-1 {
  margin: 54px auto 0;
}

.u-section-3 .u-list-1 {
  margin-top: 47px;
  margin-bottom: 60px;
}

.u-section-3 .u-repeater-1 {
  min-height: 666px;
  grid-template-columns: repeat(3, calc(33.3333% - 21.3333px));
  grid-auto-columns: calc(33.3333% - 21.3333px);
  grid-gap: 32px;
}

.u-section-3 .u-list-item-1 {
  box-shadow: 0px 0px 20px 0px rgba(128,128,128,0.4);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  transition-duration: 0.5s;
  --radius: 30px;
  background-image: none;
}

.u-section-3 .u-container-layout-1 {
  padding: 40px 20px;
}

.u-section-3 .u-text-2 {
  box-shadow: 0px 0px 0px 0 rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  font-weight: 700;
  font-size: 1.875rem;
  margin: 0;
}

.u-section-3 .u-text-3 {
  box-shadow: 0px 0px 0px  rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  margin: 20px 0 0;
}

.u-section-3 .u-list-item-2 {
  box-shadow: 0px 0px 20px 0px rgba(128,128,128,0.4);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  transition-duration: 0.5s;
  --radius: 30px;
  background-image: none;
}

.u-section-3 .u-container-layout-2 {
  padding: 40px 20px;
}

.u-section-3 .u-text-4 {
  box-shadow: 0px 0px 0px 0 rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  font-weight: 700;
  font-size: 1.875rem;
  margin: 0;
}

.u-section-3 .u-text-5 {
  font-weight: 400;
  font-style: normal;
  box-shadow: 0px 0px 0px  rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  margin: 20px 0 0;
}

.u-section-3 .u-list-item-3 {
  box-shadow: 0px 0px 20px 0px rgba(128,128,128,0.4);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  transition-duration: 0.5s;
  --radius: 30px;
  background-image: none;
}

.u-section-3 .u-container-layout-3 {
  padding: 40px 20px;
}

.u-section-3 .u-text-6 {
  box-shadow: 0px 0px 0px 0 rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  font-weight: 700;
  font-size: 1.875rem;
  margin: 0;
}

.u-section-3 .u-text-7 {
  font-weight: 400;
  font-style: normal;
  box-shadow: 0px 0px 0px  rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  margin: 20px 0 0;
}

.u-section-3 .u-list-item-4 {
  box-shadow: 0px 0px 20px 0px rgba(128,128,128,0.4);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  transition-duration: 0.5s;
  --radius: 30px;
  background-image: none;
}

.u-section-3 .u-container-layout-4 {
  padding: 40px 20px;
}

.u-section-3 .u-text-8 {
  box-shadow: 0px 0px 0px 0 rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  font-weight: 700;
  font-size: 1.875rem;
  margin: 0;
}

.u-section-3 .u-text-9 {
  font-weight: 400;
  font-style: normal;
  box-shadow: 0px 0px 0px  rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  margin: 20px 0 0;
}

.u-section-3 .u-list-item-5 {
  box-shadow: 0px 0px 20px 0px rgba(128,128,128,0.4);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  transition-duration: 0.5s;
  --radius: 30px;
  background-image: none;
}

.u-section-3 .u-container-layout-5 {
  padding: 40px 20px;
}

.u-section-3 .u-text-10 {
  box-shadow: 0px 0px 0px 0 rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  font-weight: 700;
  font-size: 1.875rem;
  margin: 0;
}

.u-section-3 .u-text-11 {
  font-weight: 400;
  font-style: normal;
  box-shadow: 0px 0px 0px  rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  margin: 20px 0 0;
}

.u-section-3 .u-list-item-6 {
  box-shadow: 0px 0px 20px 0px rgba(128,128,128,0.4);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  transition-duration: 0.5s;
  --radius: 30px;
  background-image: none;
}

.u-section-3 .u-container-layout-6 {
  padding: 40px 20px;
}

.u-section-3 .u-text-12 {
  box-shadow: 0px 0px 0px 0 rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  font-weight: 700;
  font-size: 1.875rem;
  margin: 0;
}

.u-section-3 .u-text-13 {
  font-weight: 400;
  font-style: normal;
  box-shadow: 0px 0px 0px  rgba(0,0,0,0);
  text-shadow: 0px 0px 0px rgba(0,0,0,0);
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-repeater-1 {
    grid-template-columns: repeat(3, calc(33.333333333333336% - 21.3333px));
    grid-auto-columns: calc(33.333333333333336% - 21.3333px);
    min-height: 548px;
  }

  .u-section-3 .u-list-item-1 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 300px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
  }

  .u-section-3 .u-list-item-2 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 300px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
  }

  .u-section-3 .u-list-item-3 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 300px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
  }

  .u-section-3 .u-list-item-4 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 300px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
  }

  .u-section-3 .u-list-item-5 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 300px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
  }

  .u-section-3 .u-list-item-6 {
    --animation-custom_in-translate_x: 0px;
    --animation-custom_in-translate_y: 300px;
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1199px;
  }

  .u-section-3 .u-repeater-1 {
    grid-template-columns: repeat(2, calc(50% - 15.999975000000001px));
    grid-auto-columns: calc(50% - 15.999975000000001px);
    min-height: 976px;
  }

  .u-section-3 .u-container-layout-1 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-3 .u-container-layout-2 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-3 .u-container-layout-3 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-3 .u-container-layout-4 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-3 .u-container-layout-5 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-3 .u-container-layout-6 {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-repeater-1 {
    grid-template-columns: 100%;
    grid-auto-columns: calc(100% - 0px);
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-3 .u-text-2 {
    font-size: 1.5rem;
  }

  .u-section-3 .u-text-4 {
    font-size: 1.5rem;
  }

  .u-section-3 .u-text-6 {
    font-size: 1.5rem;
  }

  .u-section-3 .u-text-8 {
    font-size: 1.5rem;
  }

  .u-section-3 .u-text-10 {
    font-size: 1.5rem;
  }

  .u-section-3 .u-text-12 {
    font-size: 1.5rem;
  }
}

.u-section-3 .u-list-item-1,
.u-section-3 .u-list-item-1:before,
.u-section-3 .u-list-item-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-2,
.u-section-3 .u-list-item-2:before,
.u-section-3 .u-list-item-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-3,
.u-section-3 .u-list-item-3:before,
.u-section-3 .u-list-item-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-4,
.u-section-3 .u-list-item-4:before,
.u-section-3 .u-list-item-4 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-5,
.u-section-3 .u-list-item-5:before,
.u-section-3 .u-list-item-5 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-6,
.u-section-3 .u-list-item-6:before,
.u-section-3 .u-list-item-6 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}.u-section-4 .u-sheet-1 {
  min-height: 542px;
}

.u-section-4 .u-layout-wrap-1 {
  margin-top: 60px;
  margin-bottom: 0;
}

.u-section-4 .u-layout-cell-1 {
  min-height: 482px;
  --radius: 50px;
  --animation-custom_in-translate_x: -300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-4 .u-container-layout-1 {
  padding: 0 30px;
}

.u-section-4 .u-image-1 {
  object-position: 50% 5.54%;
  width: 497px;
  height: 401px;
  margin: 48px auto 0;
}

.u-section-4 .u-layout-cell-2 {
  min-height: 475px;
  --animation-custom_in-translate_x: 300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-4 .u-container-layout-2 {
  padding: 50px 30px 50px 50px;
}

.u-section-4 .u-text-1 {
  font-size: 3rem;
  margin: 11px 0 0;
}

.u-section-4 .u-text-2 {
  margin: 30px 0 0;
}

@media (max-width: 1199px) {
  .u-section-4 .u-sheet-1 {
    min-height: 512px;
  }

  .u-section-4 .u-layout-cell-1 {
    background-position: 50% 50%;
    min-height: 397px;
  }

  .u-section-4 .u-image-1 {
    inset: auto;
    width: 426px;
    height: 344px;
  }

  .u-section-4 .u-layout-cell-2 {
    min-height: 392px;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-sheet-1 {
    min-height: 810px;
  }

  .u-section-4 .u-layout-wrap-1 {
    margin-top: 0;
    margin-bottom: 60px;
  }

  .u-section-4 .u-layout-cell-1 {
    min-height: 589px;
  }

  .u-section-4 .u-layout-cell-2 {
    min-height: 325px;
  }

  .u-section-4 .u-container-layout-2 {
    padding-top: 30px;
    padding-bottom: 30px;
    padding-left: 30px;
  }

  .u-section-4 .u-text-1 {
    width: auto;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-sheet-1 {
    min-height: 720px;
  }

  .u-section-4 .u-layout-cell-1 {
    min-height: 442px;
  }

  .u-section-4 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-4 .u-container-layout-2 {
    padding-right: 10px;
    padding-left: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-sheet-1 {
    min-height: 814px;
  }

  .u-section-4 .u-layout-cell-1 {
    min-height: 278px;
  }

  .u-section-4 .u-image-1 {
    width: 320px;
    height: 258px;
  }

  .u-section-4 .u-text-1 {
    font-size: 2.125rem;
  }

  .u-section-4 .u-text-2 {
    width: auto;
  }
} .u-section-5 {
  background-image: none;
}

.u-section-5 .u-sheet-1 {
  min-height: 568px;
}

.u-section-5 .u-group-1 {
  min-height: 408px;
  height: auto;
  width: 1010px;
  box-shadow: 5px 5px 30px 0 rgba(0,0,0,0.3);
  --radius: 30px;
  --animation-custom_in-translate_x: -300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 80px auto 60px;
}

.u-section-5 .u-container-layout-1 {
  padding: 30px 40px;
}

.u-section-5 .u-text-1 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 11px auto 0;
}

.u-section-5 .u-text-2 {
  font-size: 1.125rem;
  margin: 30px 0 0;
}

.u-section-5 .u-btn-1 {
  font-size: 1.125rem;
  font-weight: 700;
  text-transform: none;
  letter-spacing: normal;
  border-style: solid;
  margin: 30px auto 0;
  padding: 15px 56px 17px 55px;
}

@media (max-width: 1199px) {
  .u-section-5 .u-group-1 {
    width: 940px;
    height: auto;
  }
}

@media (max-width: 991px) {
  .u-section-5 .u-group-1 {
    width: 720px;
  }

  .u-section-5 .u-container-layout-1 {
    padding-left: 30px;
    padding-right: 30px;
  }
}

@media (max-width: 767px) {
  .u-section-5 .u-group-1 {
    width: 540px;
  }

  .u-section-5 .u-container-layout-1 {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (max-width: 575px) {
  .u-section-5 .u-group-1 {
    width: 340px;
  }
}