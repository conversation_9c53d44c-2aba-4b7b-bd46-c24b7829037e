 .u-section-1 {
  background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('images/5a13caf8bf17cc20cf99f5d1554c5dde8b3891a738f80a0b726f16779544d4aabe88c1367791bfba6310b1021598600215accf4805552d6993770f_1280.jpg');
  background-position: 50% 50%;
}

.u-section-1 .u-sheet-1 {
  min-height: 629px;
}

.u-section-1 .u-text-1 {
  font-size: 2.25rem;
  margin: 141px auto 0;
}

.u-section-1 .u-text-2 {
  font-size: 1.875rem;
  margin: 61px 0 0;
}

.u-section-1 .u-btn-1 {
  background-image: none;
  border-style: solid;
  --radius: 10px;
  margin: 59px auto 60px;
  padding: 13px 58px 15px 57px;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 519px;
  }

  .u-section-1 .u-text-1 {
    margin-top: 80px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 524px;
  }

  .u-section-1 .u-text-2 {
    font-size: 1.5rem;
    width: auto;
    margin-right: 201px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 430px;
  }

  .u-section-1 .u-text-2 {
    margin-right: 21px;
  }

  .u-section-1 .u-btn-1 {
    margin-top: 97px;
    margin-bottom: 57px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 331px;
  }

  .u-section-1 .u-text-2 {
    margin-right: 0;
  }

  .u-section-1 .u-btn-1 {
    margin-bottom: 60px;
  }
}